import json 
from IBTool import IBTool
from Comm import *
import sys
from PyQt6.QtWidgets import QApplication, QWidget
from PyQt6.QtCore import QTimer,QMetaObject, Qt, Q_ARG, QObject, pyqtSignal
from UI import *
from typing import cast
from Strategy import StrategyManager,Strategy, Strategy_Buy_When_Break_Through
from pathlib import Path
from Logger import Logger
import Global_Values
from ibapi.ticktype import TickTypeEnum
import asyncio
from threading import Thread, Event
from ibapi.contract import Contract, ContractDetails

class TradeTool(QObject):
    
    strategiesChanged = pyqtSignal(list)
    uiOnLoad = pyqtSignal()


    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.config = Global_Values.G_Config
  
        self.ib =  IBTool(self.config)
       
        self.strategyManager = StrategyManager(self.config, self.ib)
        self.strategyManager.setOnChnage(self.StrategyManager_OnChange)
       
        self.uiOnLoad.connect(self._onLoad)

        self.watchListReqIdUIMapping= {}

    def run (self):
        self.app = QApplication(sys.argv)
        self.ui = MainWindow(self.config,   self.MainWindow_OnLoad,
                                            self.MainWindow_OnClose,
                                            self.WatchList_RowAdd,
                                            self.WatchList_RowEdit,
                                            self.WatchList_RowDelete, 
                                            self.WatchList_RowSelected,
                                            self.Chart_OnUpdateVisibility,
                                            self.Chart_OnChartViewReady,
                                            self.StrategyList_RowAdd,
                                            self.StrategyList_RowEdit,
                                            self.StrategyList_RowDelete,
                                            self.StrategyList_RowStart,
                                            self.StrategyList_RowPause,
                                            self.StrategyList_RowStop
                                         )        
        


        self.ui.show()

        Global_Values.G_Logger = Logger(self.config, Global_Values.G_IsDebug)
        Global_Values.G_Logger.setUILogDisplay(self.ui)

        self.strategiesChanged.connect(self.ui.drawStrategyList)
        

        return self.app.exec()


    def  _onLoad(self):
        self.load()

   

        if  self.ui.watchList.rowCount() > 1:
                self.ui.watchList.selectRow(0)

                self.WatchList_RowSelected(self.ui, 0, 0)

        asyncio.run(self.setWatchListDisplayValueUpdaters())

        self.strategyManager.start()
        self.ui.loadingScreen.uiHide.emit() 


    def onLoad(self):
        self.ui.loadingScreen.uiShow.emit() 
        self.ib.reconnect()
        req = self.ib.reqAccountUpdates("")
        req.done.delay_wait()
        Global_Values.G_Logger.log("TradeTool",  "Account data is updated", Logger.Level.GENERAL)
        self.uiOnLoad.emit()

    def MainWindow_OnLoad(self, mainWindow:MainWindow):
        thead = Thread(target=self.onLoad, args=(), daemon=True, name="TradeTool OnLoad")
        thead.start()

    def MainWindow_OnClose(self, mainWindow:MainWindow):
    
        self.strategyManager.stop()
        if self.strategyManager.thread is not None:
            self.strategyManager.thread.join()

        req = self.ib.cancelAccountUpdates("")
        req.done.delay_wait()
        self.ib.disconnect()

        del(Global_Values.G_Logger)

        QApplication.quit()


    def StrategyList_RowAdd(self, mainWindow:MainWindow, mode, index):
        
        # req = self.ib.reqCurrentTime() 
        # req.done.delay_wait()

        # import datetime
        # import pytz
        # from zoneinfo import ZoneInfo
        # # Create a UTC datetime
        # utc_dt = datetime.datetime.fromtimestamp(req.response.contents[0], tz=ZoneInfo('UTC'))

        # # Convert to US Eastern (handles DST automatically)
        # eastern_dt = utc_dt.astimezone(ZoneInfo(self.config['TimeZone']))

        # # Format as yyyy-mm-dd hhMMss in Eastern
        # formatted = eastern_dt.strftime('%Y-%m-%d %H%M%S')
        # print(formatted)
        # print("---------------------")

        # print(self.ib.get_tws_timezone())
        # return

        watchListCurRow = mainWindow.watchList.currentRow()
        if watchListCurRow == -1 or watchListCurRow == mainWindow.watchList.rowCount()-1:
            return

        contractDetail:ContractDetails =  mainWindow.watchList.item(watchListCurRow, 0).data(Qt.ItemDataRole.UserRole)["contractDetail"]
        self.openStrategyForm(mainWindow = mainWindow, contractDetail = contractDetail,  mode = mode)
    
    def StrategyList_RowEdit(self, mainWindow:MainWindow, mode, index, strategy:Strategy):
        self.openStrategyForm(mainWindow = mainWindow, contractDetail = strategy.contractDetail,  mode = mode, index = index, last_strategy = strategy)

    def StrategyList_RowDelete(self, mainWindow:MainWindow, index, strategy:Strategy):
        self.ui.loadingScreen.uiShow.emit()
        self.strategyManager.remove(strategy)

      

     

    def StrategyList_RowStart(self, mainWindow:MainWindow, index, strategy:Strategy):
        strategy.start()
        mainWindow.drawStrategyList(self.strategyManager.get_strategy_by_contract(strategy.contractDetail))
        Global_Values.G_Logger.log("TradeTool",  f"Strategy  {strategy.__class__.__name__} is started:", Logger.Level.GENERAL)
    

    def StrategyList_RowPause(self, mainWindow:MainWindow, index,  strategy:Strategy ):
        strategy.pause()
        mainWindow.drawStrategyList(self.strategyManager.get_strategy_by_contract(strategy.contractDetail))
        Global_Values.G_Logger.log("TradeTool",  f"Strategy  {strategy.__class__.__name__} is paused:", Logger.Level.GENERAL)

    def StrategyList_RowStop(self, mainWindow:MainWindow, index, strategy:Strategy):
        strategy.stop()
        mainWindow.drawStrategyList(self.strategyManager.get_strategy_by_contract(strategy.contractDetail))
        Global_Values.G_Logger.log("TradeTool",  f"Strategy  {strategy.__class__.__name__} is stopped:", Logger.Level.GENERAL)


    def openStrategyForm(self,mainWindow:MainWindow,contractDetail:ContractDetails, mode="add", index=None, last_strategy=None, ):
        
        form = StrategyForm(mode, last_strategy, contractDetail, mainWindow,
                            self.StrategyForm_OnLoad, 
                            self.StrategyForm_OK_OnClicked)
        
        if form.exec() == QDialog.DialogCode.Accepted:
          
            if mode == "add":
                cls =  STRATEGY_FORM_LIST[form.comboStrategy.currentIndex()]["class"]
                
                _id =  self.strategyManager.get_next_id()
            
                parms = form.detailForm.getData()

                new_strategy = cls(self.ib, contractDetail, _id)
                new_strategy.setParms(parms)
            
                self.strategyManager.add(new_strategy)
                

            elif mode == "edit" and index is not None:
                parms = form.detailForm.getData()
                self.strategyManager.update(last_strategy, parms)
        

          
        form.deleteLater()
    
    def StrategyManager_OnChange(self,  strategyManager:StrategyManager, mode: str, strategy:Strategy):

        displayStrategies = self.strategyManager.get_strategy_by_contract(strategy.contractDetail)

        self.strategiesChanged.emit(displayStrategies)


        self.ui.loadingScreen.uiHide.emit()

        Global_Values.G_Logger.log("TradeTool",  f"{strategy.__class__.__name__} is {mode}ed. symbol :{strategy.contractDetail.contract.symbol}", Logger.Level.GENERAL)
    
    def StrategyForm_OnLoad(self, strategyForm:StrategyForm):
        if strategyForm.mode == "edit" and strategyForm.strategy is not None:

            for item in  STRATEGY_FORM_LIST:
                if item["class"] == strategyForm.strategy.__class__:
                    index = strategyForm.strategy_types.index(item["name"])
                    strategyForm.comboStrategy.setCurrentIndex(index)
                    break

            strategyForm.comboStrategy.setCurrentIndex(index)
            strategyForm.comboStrategy.setEnabled(False)    

            
        

    def StrategyForm_OK_OnClicked(self, strategyForm:StrategyForm):
        detailFromErrMsg = strategyForm.detailForm.checkData()

        errMsg = ""
        if strategyForm.comboStrategy.currentIndex() == -1:
            errMsg = "Please select a strategy"

        elif detailFromErrMsg is not None:
            errMsg = detailFromErrMsg

        if errMsg != "":
            QMessageBox.information(strategyForm, "Error", errMsg)
            return
        else:
            strategyForm.accept()


    def OpenSearchForm(self, mainWindow:MainWindow,  mode, row_index):
        row_data = None
        if mode == "edit" and row_index is not None:
            row_data = mainWindow.watchList.item(row_index, 0).data(Qt.ItemDataRole.UserRole)
        dialog = SearchForm(mode, row_data, mainWindow,
                            self.SearchForm_OnLoad, 
                            self.SearchForm_OK_OnClicked)
        result = dialog.exec()
        if result:
            data:dict = dialog.getData()
            self.ui.loadingScreen.uiShow.emit()
            contractDetail:ContractDetails = data["contractDetail"]
            
            if mode == "add":
                mainWindow.watchListAddRow(data)
                
                asyncio.run(self.addWatchListDisplayValueUpdater())
                
              
                def _selectRow():
                    idx = mainWindow.watchList.rowCount() 
                    if  idx > 1:
                        mainWindow.watchList.selectRow(idx-2)
                        # self.WatchList_RowSelected(mainWindow, idx-2, 0)


                QTimer.singleShot(100, _selectRow)

                Global_Values.G_Logger.log("",  f"Watch Item is added: {contractDetail.contract.symbol}", Logger.Level.GENERAL)
     
            elif mode == "edit":
                item = mainWindow.watchList.item(row_index, 0)
                
                item.setText(contractDetail.contract.symbol)
                item.setData(Qt.ItemDataRole.UserRole, data)

                asyncio.run(self.updateWatchListDisplayValueUpdater(row_index))

                Global_Values.G_Logger.log("",  f"Watch Item is updated: {contractDetail.contract.symbol}", Logger.Level.GENERAL)

            self.save()
            self.ui.loadingScreen.uiHide.emit()
        dialog.deleteLater()
        return result

    def WatchList_RowAdd(self, mainWindow:MainWindow, mode, row_index):
        self.OpenSearchForm(mainWindow, mode, row_index)


    def WatchList_RowEdit(self, mainWindow:MainWindow, mode, row_index):    
        self.OpenSearchForm(mainWindow, mode, row_index)

    def WatchList_RowDelete(self, mainWindow:MainWindow, row_index):
        
        contractDetail:ContractDetails =  mainWindow.watchList.item(row_index, 0).data(Qt.temDataRole.UserRole)["contractDetail"]
        contract = contractDetail.contractI
        strategies = self.strategyManager.get_strategy_by_contract(contractDetail)
        if len(strategies) > 0:
            QMessageBox.information(mainWindow, "Error", "Please delete all strategies before deleting the watch list item.")
            return
        
        self.ui.loadingScreen.uiShow.emit()
        asyncio.run(self.deleteWatchListDisplayValueUpdater(row_index))
        mainWindow.watchList.removeRow(row_index)
        
        self.save()
        Global_Values.G_Logger.log("TradeTool",  f"Watch Item : {contract.symbol} is deleted", Logger.Level.GENERAL)
        self.ui.loadingScreen.uiHide.emit()

    def WatchList_RowSelected(self, mainWindow:MainWindow, row, col):
        if row == mainWindow.watchList.rowCount()-1:    
            return

        cellWidget = mainWindow.watchList.cellWidget(row, 0)
        
        if cellWidget and isinstance(cellWidget, QPushButton) and cellWidget.text() == "Add":
            return
        
        contractDetail: ContractDetails = mainWindow.watchList.item(row, 0).data(Qt.ItemDataRole.UserRole)["contractDetail"] 


        for chartWidget in [mainWindow.dailyChart, mainWindow.weeklyChart, mainWindow.fiveMinsChart, mainWindow.oneSecChart]:
            chartWidget.updateVisibility()

        mainWindow.drawStrategyList(self.strategyManager.get_strategy_by_contract(contractDetail))
        
    def SearchForm_OnLoad(self, searchForm:SearchForm):
        if searchForm.mode == 'edit' and searchForm.row_data is not None:
            contractDetail : ContractDetails =   searchForm.row_data["contractDetail"]
            contract = contractDetail.contract
            searchForm.symbolEdit.setText(contract.symbol)
            index = searchForm.secTypeComboEdit.findData(contract.secType)
            if index != -1:
                searchForm.secTypeComboEdit.setCurrentIndex(index)

            
            searchForm.exchangeEdit.setCurrentText(contract.exchange)
            searchForm.currencyEdit.setCurrentText(contract.currency)
            searchForm.primaryExchangeEdit.setCurrentText(contract.primaryExchange)     

    def SearchForm_OK_OnClicked(self, searchForm:SearchForm):
        errMsg = ""
        if  searchForm.symbolEdit.text() == "":
            errMsg += "Symbol is empty"
       
        elif self.ib.accountData[self.ib.accountName].get(('TotalCashBalance',searchForm.currencyEdit.currentText())) is None:
            errMsg += f"Your account does not has the Currency {searchForm.currencyEdit.currentText()}, please buy some first."

        if errMsg != "": 
            QMessageBox.information(searchForm, "Error", errMsg)
            return


        if searchForm.mode == "add":
            mainWindow = cast(MainWindow, searchForm.parent_QWidget)
            for i in range(mainWindow.watchList.rowCount()-1):
                item = mainWindow.watchList.item(i, 0)
                if item is not None:
                    if item.data(Qt.ItemDataRole.UserRole)["contractDetail"].contract.symbol == searchForm.symbolEdit.text().upper():
                        QMessageBox.information(searchForm, "Error", "Symbol already exists")
                        return
        
        self.ui.loadingScreen.uiShow.emit()

        reqId  = self.ib.get_next_reqID()
        contract = Contract()
        contract.symbol = searchForm.symbolEdit.text().upper()
        contract.secType = searchForm.secTypeComboEdit.currentData()
        contract.exchange = searchForm.exchangeEdit.currentText()
        contract.currency = searchForm.currencyEdit.currentText()
        contract.primaryExchange = searchForm.primaryExchangeEdit.currentText()

        req = self.ib.reqContractDetails(reqId,contract)
        req.done.delay_wait()
        

        if len(req.response.errors) == 0:
                contractDetail = req.response.contents[0]

                if searchForm.mode == "add":
                    searchForm.row_data = {"contractDetail": contractDetail }
                    
                else:
                    searchForm.row_data["contractDetail"] = contractDetail
                    searchForm.row_data["req"] = req

                searchForm.accept()

                self.ui.loadingScreen.uiHide.emit()
        else:
            self.ui.loadingScreen.uiHide.emit()
            QMessageBox.information(searchForm, "Error", req.response.errors[0]['errorString'])


    async def addWatchListDisplayValueUpdater(self):
        row_index = self.ui.watchList.rowCount()-2
            
        await self.setWatchListDisplayValueUpdater(row_index)
    # ************************************************************************
    async def updateWatchListDisplayValueUpdater(self, row_index):

        rowData = self.findWatchListReqIdUIMapping(row_index)
       
        
        if rowData is not None and rowData["reqUpdate"] is not None:
            req = self.ib.cancelMktData(rowData["reqUpdate"].reqId)
            await asyncio.to_thread(req.done.delay_wait)

            del self.watchListReqIdUIMapping[req.reqId]

        await self.setWatchListDisplayValueUpdater(row_index)
        
    async def  deleteWatchListDisplayValueUpdater(self, row_index):

        reqUpdate = self.findWatchListReqIdUIMapping(row_index)["reqUpdate"]

        req = self.ib.cancelMktData(reqUpdate.reqId)
        await asyncio.to_thread(req.done.delay_wait)
        del self.watchListReqIdUIMapping[reqUpdate.reqId]
    
    async def  setWatchListDisplayValueUpdater(self, row_index):

        req = self.ib.reqMarketDataType(1)
        await asyncio.to_thread(req.done.delay_wait)
        
     
        item = self.ui.watchList.item(row_index, 0)
        contract =  item.data(Qt.ItemDataRole.UserRole)["contractDetail"].contract
        reqId = self.ib.get_next_reqID()
        req = self.ib.reqMktData(reqId, contract, "", False, False, [])
        req.update_callback = self.updateWatchListDisplayValue
        self.watchListReqIdUIMapping[reqId] = {"BID_UI":self.ui.watchList.item(row_index, 1), "ASK_UI":self.ui.watchList.item(row_index, 2), "LAST_UI":self.ui.watchList.item(row_index, 3), "contract":contract, "reqUpdate": req, "rowwIndex":row_index}

        await asyncio.to_thread(req.done.delay_wait)

        if len(req.response.errors) > 0:
            req = self.ib.reqMarketDataType(3)
            await asyncio.to_thread(req.done.delay_wait)
             
            req = self.ib.reqMktData(reqId, contract, "", False, False, [])
            req.update_callback = self.updateWatchListDisplayValue
            self.watchListReqIdUIMapping[reqId]["reqUpdate"] = req

            await asyncio.to_thread(req.done.delay_wait)
            if len(req.response.errors) > 0:
                del self.watchListReqIdUIMapping[reqId]
                
        



    async def setWatchListDisplayValueUpdaters(self):

        self.watchListReqIdUIMapping.clear()
        

        req = self.ib.reqMarketDataType(1)
        await asyncio.to_thread(req.done.delay_wait)

        async def requestWork(rowData):
            req = rowData["reqUpdate"]
            await asyncio.to_thread(req.done.delay_wait)
            return rowData 


        tasks = []
        reqIds = self.ib.get_next_reqIDs(self.ui.watchList.rowCount()-1)
        for i in range(self.ui.watchList.rowCount()-1):
            item = self.ui.watchList.item(i, 0)
            contract =  item.data(Qt.ItemDataRole.UserRole)["contractDetail"].contract
            reqId = reqIds[i]
            req = self.ib.reqMktData(reqId, contract, "", False, False, [])
            req.update_callback = self.updateWatchListDisplayValue

            rowData = {"BID_UI":self.ui.watchList.item(i, 1), "ASK_UI":self.ui.watchList.item(i, 2), "LAST_UI":self.ui.watchList.item(i, 3), "contract":contract, "reqUpdate": req, "rowwIndex":i}  
            self.watchListReqIdUIMapping[reqId] = rowData

            task = asyncio.create_task(requestWork(rowData))
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        cancalTask = []

        tasks2 = []
        notAllowedRealTime = []
        for rowData in results:
            req = rowData["reqUpdate"]
            if len(req.response.errors) > 0:
                notAllowedRealTime.append(rowData)

        if len(notAllowedRealTime) > 0:
         
            
            req = self.ib.reqMarketDataType(3)
            await asyncio.to_thread(req.done.delay_wait)
            
    
            for idx, rowData in enumerate(notAllowedRealTime):

                contract = rowData["contract"]    
                
                req = self.ib.reqMktData(rowData["reqUpdate"].reqId, contract, "", False, False, [])
                req.update_callback = self.updateWatchListDisplayValue
                rowData["reqUpdate"] = req

                task = asyncio.create_task(requestWork(rowData))
                tasks2.append(task)

            results2 = await asyncio.gather(*tasks2)


            for rowData in results2:
                req = rowData["reqUpdate"]
                if len(req.response.errors) > 0:
                    del self.watchListReqIdUIMapping[rowData["reqUpdate"].reqId]
                    # rowData["reqUpdate"] = None



    def findWatchListReqIdUIMapping(self, rowIndex):
        for key, value in self.watchListReqIdUIMapping.items():
            if value["rowwIndex"] == rowIndex:
                return value
        return None
 
                    
    def updateWatchListDisplayValue(self, req, data):

        
        mappingData =  self.watchListReqIdUIMapping.get(req.reqId)
        if mappingData is None:
            return
            
        tickType = data["tickType"]

        if tickType == TickTypeEnum.BID:
            mappingData["BID_UI"].setText(f"{data['price']}")

        elif tickType == TickTypeEnum.ASK:
            mappingData["ASK_UI"].setText(f"{data['price']}")

        elif tickType == TickTypeEnum.LAST:
            mappingData["LAST_UI"].setText(f"{data['price']}")

        elif tickType == TickTypeEnum.DELAYED_BID:
            mappingData["BID_UI"].setText(f"{data['price']} D")

        elif tickType == TickTypeEnum.DELAYED_ASK:
            mappingData["ASK_UI"].setText(f"{data['price']} D")

        elif tickType == TickTypeEnum.DELAYED_LAST:
            mappingData["LAST_UI"].setText(f"{data['price']} D")


    def Chart_OnUpdateVisibility(self, chartWidget:ChartWidget):
        mainWindow = cast(MainWindow, chartWidget.parent_QWidget)
        
        row = mainWindow.watchList.currentRow()

        if row == -1 or row == mainWindow.watchList.rowCount()-1:
            return

    

        if not chartWidget.visible_flag:
            return
            

        if chartWidget.req is not None:
            req = self.ib.cancelHistoricalData(chartWidget.req.reqId)
            req.done.delay_wait()
            chartWidget.req = None
            
             
        contactDetail:ContractDetails = mainWindow.watchList.item(row, 0).data(Qt.ItemDataRole.UserRole)["contractDetail"] 
        con = contactDetail.contract
        if chartWidget.req is not None:
            if chartWidget.req.parms["contract"].symbol == con.symbol:
                return
                
            else:
                if chartWidget.req.keepAlive:
                    req = self.ib.cancelHistoricalData(chartWidget.req.reqId)
                    req.done.delay_wait()

        
        
        if self.ib.reach_ten_mins_data_request_limit():
            timeWait = 600 - (time.time() - self.ib.ten_mins_data_requests[0]["timestamp"])
            Global_Values.G_Logger.log("TradeTool",  f"Wait for 10 mins data request limit. wait for {timeWait} seconds", Logger.Level.GENERAL)
            time.sleep(timeWait)


        reqId =  self.ib.get_next_reqID()
        

        if chartWidget.chart_name == "Daily Chart":
            chartWidget.req = self.ib.reqHistoricalData(reqId=reqId, contract=con, endDateTime="", durationStr="3 Y", barSizeSetting="1 day", whatToShow="TRADES", useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])
        elif chartWidget.chart_name == "Weekly Chart":
            chartWidget.req = self.ib.reqHistoricalData(reqId=reqId, contract=con, endDateTime="", durationStr="5 Y", barSizeSetting="1 week", whatToShow="TRADES", useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])
        elif chartWidget.chart_name == "5 mins Chart":
            chartWidget.req = self.ib.reqHistoricalData(reqId=reqId, contract=con, endDateTime="", durationStr="1 W", barSizeSetting="5 mins", whatToShow="TRADES", useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])
        elif chartWidget.chart_name == "1 sec Chart":
            chartWidget.req = self.ib.reqHistoricalData(reqId=reqId, contract=con, endDateTime="", durationStr="300 S", barSizeSetting="5 secs", whatToShow="TRADES", useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])

        chartWidget.req.userData["chartWidget"] = chartWidget
        chartWidget.req.update_callback = self.chart_data_update

        chartWidget.req.done.delay_wait()
        
        data = chartWidget.req.response.contents

    

        priceData = []
        volumeData = []
        
        
        priceData, volumeData = self.build_chart_data_lc(data)



        priceData_json = json.dumps(priceData)
        volumeData_json = json.dumps(volumeData)


        with open('chart/chart_template.html', 'r', encoding='utf-8') as f:
            template = f.read()

        time_visible = "true" if chartWidget.chart_name in ["5 mins Chart", "1 sec Chart"] else "false"
        seconds_visible = "true" if chartWidget.chart_name == "1 sec Chart" else "false"
            
        template = template.replace('<!-- TIME_VISIBLE_PLACEHOLDER -->', time_visible)
        template = template.replace('<!-- SECONDS_VISIBLE_PLACEHOLDER -->', seconds_visible)
        template = template.replace('<!-- SYMBOL_PLACEHOLDER -->', con.symbol)
        template = template.replace('<!-- PRICE_DATA_PLACEHOLDER -->', priceData_json)
        template = template.replace('<!-- VOLUME_DATA_PLACEHOLDER -->', volumeData_json)

        # with open(f"chart/temp/chart_{chartWidget.chart_name}.html", 'w', encoding='utf-8') as f:
        #     f.write(template)

        base_path = os.path.abspath("chart") + os.path.sep
        base_url = QUrl.fromLocalFile(base_path)
        chartWidget.isChartViewReady = False
        chartWidget.chartView.setHtml(template,base_url)
       
        



    def build_chart_data_lc(self, data):
        if len(data) == 0:
            return [], []


        make_time_func = self.make_chart_time(data[0]["date"])

        priceData = []
        volumeData = []
        for record in data:
            priceData.append(
                    {"time":make_time_func(record["date"]), "open": record["open"], "high": record["high"], "low": record["low"], "close": record["close"]}
                )

            volumeData.append(
                {"time": make_time_func(record["date"]), "value": record["volume"], "color": '#ef5350' if record["open"] > record["close"] else  '#26a69a'}
            )  
        
        return priceData, volumeData

    def make_chart_time(self, dateStr):

        # short vs full
        if len(dateStr) <= 8:
            make_time = lambda d: f"{d[:4]}-{d[4:6]}-{d[6:8]}"
        else:
            _, tz_name = dateStr.rsplit(" ", 1)
            tz = ZoneInfo.timezone(tz_name)
            fmt = "%Y%m%d %H:%M:%S"
            dtp = datetime.strptime
            make_time = lambda d: int(dtp(d[:17], fmt).replace(tzinfo=tz).timestamp())
            
        return make_time

    def chart_data_update(self, req, newBar):
        chartWidget = req.userData["chartWidget"]
        make_time_func = self.make_chart_time(newBar["date"])
        priceData = {"time":make_time_func(newBar["date"]), "open": newBar["open"], "high": newBar["high"], "low": newBar["low"], "close": newBar["close"]}
        volumeData = {"time": make_time_func(newBar["date"]), "value": newBar["volume"], "color": '#ef5350' if newBar["open"] > newBar["close"] else  '#26a69a'}

        data = {"priceData": priceData, "volumeData": volumeData}

        js = f"update_chart_data({json.dumps(data)});"
        
        if  chartWidget.isChartViewReady:
            chartWidget.updateBarJS.emit(js)
        else:
            chartWidget.pendingJS.append(js)

   


    def Chart_OnChartViewReady(self, chartWidget:ChartWidget):
        chartWidget.isChartViewReady = True
        for js in chartWidget.pendingJS:
            chartWidget.updateBarJS.emit(js)
        chartWidget.pendingJS = []


    def save(self):
        _watchList = []

        for i in range(self.ui.watchList.rowCount()-1):
            item = self.ui.watchList.item(i, 0)
            if item is None:
                continue

            contractDetail:ContractDetails = item.data(Qt.ItemDataRole.UserRole)["contractDetail"]
            
            _contractDetail =  DataConverter.contractDetails_to_dict(contractDetail)
            _watchList.append({"idx":i, "contractDetail":_contractDetail})


        with open(self.config["WatchListFile"], "w", encoding="utf-8") as f:
            json.dump(_watchList, f, ensure_ascii=False,indent=2)

    
    def load(self):
      
        path: Path = Path(self.config["WatchListFile"])

        if not path.exists():
            Global_Values.G_Logger.log("TradeTool", f"can't find WatchList file {path}", Logger.Level.DETAIL)
           
            return

        
        with path.open("r", encoding="utf-8") as f:
            _watchList = json.load(f)              


        for i in  range(len(_watchList)):
            item = _watchList[i]
            item["contractDetail"] = DataConverter.dict_to_contractDetails(item["contractDetail"])
            self.ui.watchListAddRow(item)

        self.strategyManager.load()
    

if __name__ == "__main__":

    
    Global_Values.initConfig()

    tradeTool = TradeTool()

    sys.exit(tradeTool.run())    
       
    
