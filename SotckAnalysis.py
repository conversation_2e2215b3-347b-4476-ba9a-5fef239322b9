from BufferList import BufferList 
import pandas as pd
import pandas_ta  as ta
from ibapi.common import BarData

class SotckAnalysis():
    
    def __init__(self, bars: BufferList):
        self.bars = bars
        self.df_bar = pd.DataFrame(bars.to_list())
        self.df_bar["VOL_SMA_20"] = ta.sma(self.df_bar["volume"], length=20)


    def is_heavy_sell(self):
        if len (self.bars) == 0:
            return False

        num_of_bars = len(self.bars)
        num_of_last_bars = 5 if num_of_bars > 5 else num_of_bars

        #最近5根K线是否OverSold
        last_bar  = self.bars.get_data(-1) 
        near_highest =  last_bar.high
        for i in range(num_of_bars - 1, num_of_bars - num_of_last_bars, -1):              
            high = self.bars.get_data(i).high
            near_highest = max(near_highest, high)

            if last_bar.close / near_highest <= 0.95 :
                return True
        return False

    #refine later
    def is_weight_of_sell_decrease(self):
        return True

    def is_heavy_buy(self):
        if len (self.bars) == 0:
            return False

        num_of_bars = len(self.bars)
        num_of_last_bars = 10 if num_of_bars > 10 else num_of_bars

        avg_volume = self.df_bar["VOL_SMA_20"].iloc[-1]
        last_bar = self.bars.get_data(num_of_bars - 1)

        overTradeBarIdx = -1
        for i in range(num_of_bars - 1, num_of_bars - num_of_last_bars, -1):  
            bar = self.bars.get_data(i)
            if bar.volume > avg_volume * 5:
                overTradeBarIdx = i

       
        if overTradeBarIdx != -1:
            for i in range(num_of_bars - 1, num_of_bars - num_of_last_bars, -1):  

                lowestIn2Bars = self.bars.get_data(i - 1).low

                if len (self.bars) > 1:
                    lowestIn2Bars = min(self.bars.get_data(i-2).low, self.bars.get_data(i - 1).low)
                
                
                if bar.close > lowestIn2Bars * 1.3 and i >= overTradeBarIdx:
                    return True
             
 

    def is_break_high(self, df):
        pass

    def is_break_low(self, df):
        pass

    def is_break_ema(self, df, ema):
        pass

    def is_break_sma(self, df, sma):
        pass

