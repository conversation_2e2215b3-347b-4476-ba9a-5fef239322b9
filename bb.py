import pandas as pd
import numpy as np
import pandas_market_calendars as mcal

# -----------------------------------------------------------------------------
# 1. SCALE DOWN A SINGLE BAR
# -----------------------------------------------------------------------------
def scale_down_bar(bar: dict,
                   start_time: pd.Timestamp,
                   end_time: pd.Timestamp,
                   freq: str,
                   high_frac: float = 0.25,
                   low_frac: float = 0.75) -> pd.DataFrame:
    # 1) 构建关键时间点和价格
    timeline = pd.to_datetime([
        start_time,
        start_time + (end_time - start_time) * high_frac,
        start_time + (end_time - start_time) * low_frac,
        end_time
    ])
    prices = [bar["Open"], bar["High"], bar["Low"], bar["Close"]]
    curve = pd.Series(prices, index=timeline).sort_index()

    # 2) 目标频率的完整时间索引
    sub_index = pd.date_range(start_time, end_time, freq=freq)

   # 2) 如果末尾没包含 end_time，就手动追加
    if sub_index[-1] < end_time:
        sub_index = sub_index.append(pd.DatetimeIndex([end_time]))

    # 3) 按原逻辑插值并切片
    curve = (
        pd.Series(prices, index=timeline)
        .sort_index()
        .reindex(curve.index.union(sub_index))
        .interpolate(method="time")
        .loc[sub_index]
    )

    # 4) 对每个小区间生成 OHLC 记录
    records = []
    step = len(sub_index) - 1
    for t0, t1 in zip(sub_index[:-1], sub_index[1:]):
        seg = curve[t0:t1]
        if seg.empty:
            continue
        records.append({
            "Date":     t0,
            "Open":     seg.iloc[0],
            "High":     seg.max(),
            "Low":      seg.min(),
            "Close":    seg.iloc[-1],
            "Volume":   bar["Volume"]   / step,
            "WAP":      bar["WAP"],
            "BarCount": bar["BarCount"] / step,
        })

    return pd.DataFrame(records).set_index("Date")

def resample_bars(df: pd.DataFrame,
                  from_freq: str,
                  to_freq: str,
                  end_time: pd.Timestamp = None,
                  tradingTimeStart: str = "09:30",
                  tradingTimeEnd: str = "16:00") -> pd.DataFrame:
    """
    Resample bars from `from_freq` to `to_freq`, with optional session trading hours.
    
    For upsampling, uses tradingTimeStart and tradingTimeEnd as custom intraday boundaries.
    For downsampling, standard pandas aggregation is used.
    """
    df = df.copy()
    df.index = pd.to_datetime(df.index)

    # Optionally trim the time window
    if end_time is not None:
        df = df[df.index <= pd.to_datetime(end_time)]

    from_n = pd.Timedelta(from_freq)
    to_n = pd.Timedelta(to_freq)

    # UPSAMPLE
    if to_n < from_n:
        pieces = []
        for ts, row in df.iterrows():
            date = ts.date()
            bar_start = pd.Timestamp(f"{date} {tradingTimeStart}")
            bar_end   = pd.Timestamp(f"{date} {tradingTimeEnd}")

            pieces.append(scale_down_bar(row.to_dict(), bar_start, bar_end, to_freq))
        return pd.concat(pieces).sort_index()

    # DOWNSAMPLE
    else:
        ohlc_dict = {
            "Open": "first", "High": "max", "Low": "min", "Close": "last",
            "Volume": "sum", "BarCount": "sum"
        }
        df["WAPxV"] = df["WAP"] * df["Volume"]
        agg = df.resample(to_freq).agg(ohlc_dict)
        agg["WAP"] = agg["WAPxV"] / agg["Volume"]
        return agg.drop(columns="WAPxV")



import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
# --- (include your scale functions here: scale_down_bar, resample_bars, etc.) ---

def plot_ohlc(ax, df, width, color_up, color_down, alpha=1.0):
    """
    Draw OHLC candles on ax.
    df must have DatetimeIndex and columns Open, High, Low, Close.
    width is in days (as float), so e.g. 0.8 for 80% of one day, 1/24 for 1 hour.
    """
    # convert datetime to matplotlib float days
    dates = mdates.date2num(df.index.to_pydatetime())
    for date, o, h, l, c in zip(dates,
                                df['Open'], df['High'],
                                df['Low'], df['Close']):
        color = color_up if c >= o else color_down
        # wick
        ax.vlines(date, l, h, color=color, alpha=alpha)
        # body
        left = date - width/2
        rect = plt.Rectangle((left, min(o, c)),
                             width, abs(c - o),
                             facecolor=color, alpha=alpha)
        ax.add_patch(rect)

    # format
    ax.xaxis_date()
    ax.grid(True)
# -----------------------------------------------------------------------------
# EXAMPLE USAGE
# -----------------------------------------------------------------------------
if __name__ == "__main__":
    

    # --- build sample data ---
    raw = [
        {"Date": "2025-07-28", "Open":4323,"High":4550,"Low":4123,"Close":4123,
        "Volume":200,"WAP":4323,"BarCount":2}
        
    ]
    df_day = pd.DataFrame(raw).set_index("Date")
    df_day.index = pd.to_datetime(df_day.index)

    

    # resample daily → hourly
    df_hour = resample_bars(df_day, from_freq="1D", to_freq="1h")

    
    # --- plotting ---
    fig, ax = plt.subplots(figsize=(12, 4))

    # original bars – width = 0.8 day
    plot_ohlc(ax, df_day, width=0.8, color_up="lightgrey", color_down="lightgrey", alpha=0.7)

    # resampled bars – width = 1 hour ≈ 1/24 day
    plot_ohlc(ax, df_hour, width=1/24*0.8, color_up="steelblue", color_down="tomato", alpha=0.9)

    ax.set_title("Grey = Daily Bars (Input)   Blue/Red = Hourly Bars (Output)")



    plt.tight_layout()
    plt.show(block=True)

    