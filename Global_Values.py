from Logger import Logger
import json
import configparser
from pathlib import Path


G_IsDebug = False


G_Config:dict = None
G_Logger:Logger = None

def initConfig():

    global  G_Config
    configFile = 'config.json'
    with open(configFile) as f:
        G_Config = json.load(f)

        G_Config["TimeZone"] = get_timezone_from_tws_preferences(Path(G_Config["TWSConfig"]))


def get_timezone_from_tws_preferences(ini_path: Path) -> str | None:
    config = configparser.ConfigParser()
    config.read(ini_path, encoding="utf-8")

    try:
        return config.get("Logon", "TimeZone")
    except (configparser.NoSectionError, configparser.NoOptionError):
        raise Exception("can't find TimeZone in " + ini_path)        


   