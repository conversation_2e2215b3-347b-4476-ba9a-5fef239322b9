
import os
from Comm import *
import debugpy  

from threading import Thread, Event, RLock
from ibapi.order_cancel import OrderCancel
from ibapi.wrapper import EWrapper
from ibapi.client import EClient
from ibapi.common import TickerId, BarData, TickAttrib, TickAttribLast, MarketDataTypeEnum, ListOfHistoricalTick, ListOfHistoricalTickLast, WshEventData
from ibapi.contract import Contract, ContractDetails
from ibapi.order import Order
from ibapi.order_state import OrderState
from ibapi.execution import ExecutionFilter
from ibapi.commission_report import CommissionReport
from ibapi.ticktype import TickTypeEnum

from zoneinfo import ZoneInfo
from datetime import timedelta

from functools import wraps

from pandas import DataFrame as df
from ibapi.tag_value import TagValue 

from typing import Any

import traceback

from collections import defaultdict, deque
from decimal import Decimal

from BufferList import BufferList
import pytz
import re 
from pathlib import Path

from typing import List
ListOfTagValue = List[TagValue]

import Global_Values
from Logger import Logger

# import traceback
# from types import MethodType
# class DebugList(list):
#     def remove(self, value):
#         # if value.parms.get("contract") is not None and value.parms["contract"].symbol == "TSM":
#         if value.type == REQUEST_TYPES.GET_HISTORICAL_DATA:
#             print (f"Removed reqId: {value.reqId} type: {value.type} symbol: {value.parms['contract'].symbol}")
            
#             print(f"DEBUG: {self!r}.remove({value!r}) called")
#             traceback.print_stack(limit=30)
#         super().remove(value)

#     def append(self, value):
#         # if value.parms.get("contract") is not None and value.parms["contract"].symbol == "TSM":
#         if value.type == REQUEST_TYPES.GET_HISTORICAL_DATA:
#             print (f"added reqId: {value.reqId} type: {value.type} symbol: {value.parms['contract'].symbol}")
#             print(f"DEBUG: {self!r}.append({value!r}) called")
#             traceback.print_stack(limit=30)
#         super().append(value)

class IBTool(EClient, EWrapper):
    def __init__(self, config):
        EClient.__init__(self, self)
        self.config = config
        self.nextOrderId = 0
        self.accountSummaryData = {}
        self.accountData = {}
        self.protfolioData = {}
        self.accountUpdateTime = ""
        self.openOrdersData = {}
        self.openStatusData = {}
        self.execDetailsData = {}
        self.completedOrdersData = {}
        self.barsData = {}
        self.accountName = ""

        self.requests =  []
        
        #only store Request that have reqId, because less resource is used for high frequency call
        self.reqIDMapping = {}

        self.sys_current_time = 0
        self.ten_mins_data_requests= []

        self.TWS_connection = False
        self.TWS_aviliable = True
        self.thread_run = None
        self.thread_connection = None

        self.mktData = {}

        self.twsTimeZone = ZoneInfo(self.config['TimeZone'])

        self.lock = RLock()

        self.patternMessageDone =  self.get_message_done_pattern()

        # self.requests = DebugList(self.requests)
 


    def register_requests(self, type: REQUEST_TYPES, reqId: int = -1, keepAlive: bool = False, timeout: float = 0.0, delayWhenFinished: float = 0.0 , noResponse: bool = False , parms: dict = {}):
        if type not in REQUEST_TYPES_ALLOW_DUPLICATE.LIST:
            if self.find_unduplicate_running_request( type):
                raise Exception(f"Request { self.get_request_type_name(type) }, no more than one unduplicated request allowed.")

        if type in REQUEST_DATA_NUM_ALIVE_LIMIT.TYPE_LIST:
            if self.reach_max_data_alive_request_limit(type):
                raise Exception("Reach max alive request limit")
 

        req = Request(type, reqId = reqId, keepAlive = keepAlive, timeout = timeout, delayWhenFinished =  delayWhenFinished , callbackWhenFinished = self.unregister_requests_nonKeepAlive_pendingDelete, noResponse = noResponse, parms = parms)
        
        with self.lock:

            self.requests.append(req)

            if reqId != -1:
                if type == REQUEST_TYPES.GET_MARKET_DATA  or  type == REQUEST_TYPES.GET_HISTORICAL_DATA or type == REQUEST_TYPES.GET_HISTORICAL_TICKS or type == REQUEST_TYPES.GET_TICK_BY_TICK_DATA or type == REQUEST_TYPES.GET_REAL_TIME_BARS:
                    if reqId in self.reqIDMapping.keys():
                        raise Exception(f"Request in reqIDMapping { self.get_request_type_name(type) } already exists and is not allowed to be duplicated.")

                    self.reqIDMapping[req.reqId] = req

            if type in REQUEST_DATA_NUM_TEN_MINS_LIMIT.TYPE_LIST:
                self.register_ten_mins_data_request(reqId)

        return req

       

    def unregister_requests_nonKeepAlive_pendingDelete(self, reqDel: Request ):
    
        with self.lock:
            delList = []

            for req in self.requests:
                if (reqDel.type == req.type and reqDel.reqId == -1) or  reqDel.reqId == req.reqId:
                    if  req.pendingDelete == True or req.keepAlive == False :

                        delList.append(req)

            
            for req in delList:
                
                self.requests.remove(req)
                if  self.reqIDMapping.get(reqDel.reqId) is not None:
                    del self.reqIDMapping[reqDel.reqId]


    def find_request(self, type: REQUEST_TYPES, reqId: int = -1):
        for req in self.requests:
            if req.type == type and (reqId == -1 or req.reqId == reqId) : 
                return req
        return None
    
    def find_request_by_reqId(self, reqId: int ):
        for req in self.requests:
            if req.reqId == reqId:
                return req
        return None
    
    def find_request_by_type_list(self, type_list: list[REQUEST_TYPES], reqId: int = -1):
        for req in self.requests:
            if req.type in type_list and (reqId == -1 or req.reqId == reqId):
                return req
        return None
    
    def find_unduplicate_running_request(self, type:REQUEST_TYPES):
        for req in self.requests:
            if  type == req.type and req.type not in REQUEST_TYPES_ALLOW_DUPLICATE.LIST:
                return req
        
        return None

    def find_last_unfinished_request(self):

        for i in range(len(self.requests)-1, -1, -1):
            if  not self.requests[i].done.is_set():
                return self.requests[i]

        return None


    def get_request_type_name(self, value):
        for name, val in REQUEST_TYPES.__dict__.items():
            if val == value:
                return name
        return None

    def get_next_reqID(self): 
        with self.lock:
            for i in range(TickerID_RULES.REQUEST_ID_MIN, TickerID_RULES.REQUEST_ID_MAX):
                if self.find_request_by_reqId(i) is None:
                    return i
        
        raise Exception("No more request ID available")
        
    def get_next_reqIDs(self, num):
        with self.lock:

            reqIds = []
            if num == 0:
                return reqIds

            for i in range(TickerID_RULES.REQUEST_ID_MIN, TickerID_RULES.REQUEST_ID_MAX):
                if self.find_request_by_reqId(i) is None:
                    reqIds.append(i)
            
                    if len(reqIds) >= num:
                        return reqIds

        raise Exception(f"No more request ID available, need {num}")
    
    def reach_max_data_alive_request_limit(self, type):
        count = 0

        for req in self.requests:
            if req.type in REQUEST_DATA_NUM_ALIVE_LIMIT.TYPE_LIST:
                count += 1

        if count >= REQUEST_DATA_NUM_ALIVE_LIMIT.MAX:
            return True
        else:
            return False

       
    def register_ten_mins_data_request(self, reqId: int):
        self.ten_mins_data_requests.append({"reqId": reqId, "timestamp": time.time()})

    def reach_ten_mins_data_request_limit(self):
        count = 0
        for r in  self.ten_mins_data_requests[:]:
            dif = time.time() - r["timestamp"]
            if dif > 600:
                self.ten_mins_data_requests.remove(r)
            else:
                count += 1

        if count > REQUEST_DATA_NUM_TEN_MINS_LIMIT.MAX:
            return True
        else:
            return False


    @staticmethod
    def RequestWarper(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):

            retry_count = 0
            max_retry = 300

            # Logger.log(f"Wrapper Start Request {func.__name__} is waiting for the connection OK. isConnected:{super().isConnected()} TWS_connection: {self.TWS_connection}, TWS_aviliable: {self.TWS_aviliable}")
            while retry_count < max_retry:
                if not (self.TWS_connection  and super().isConnected() and self.TWS_aviliable) :
                    
                    if not (self.TWS_connection  and super().isConnected()):
                        if self.thread_connection is  None or not self.thread_connection.is_alive():
                            self.thread_connection = Thread(target=self.reconnect, daemon=True , name="Reconnect")
                            self.thread_connection.start()


                    # if retry_count % 10 == 0:
                    #     Logger.log(f"Request {func.__name__} is waiting for the connection OK. isConnected:{super().isConnected()} TWS_connection: {self.TWS_connection}, TWS_aviliable: {self.TWS_aviliable}")

                    retry_count += 1
                    time.sleep(1)

                    if retry_count == max_retry:
                        req = Request(REQUEST_TYPES.REQUEST_ERROR)
                        req.response = Response()   
                        req.response.errors.append(CLIENT_REQUEST_ERRORS.CONNECTION_UNAVAILABLE)
                        return req
                else:
                    break
            
            if self.thread_connection is not None and self.thread_connection.is_alive():
                self.thread_connection.join()

            Global_Values.G_Logger.log(f"Req {func.__name__}", f"{args} {kwargs}", Logger.Level.DETAIL)
            return func(self, *args, **kwargs)
        return wrapper



    @RequestWarper
    def placeOrder(self, contract: Contract, order: Order):
        Global_Values.G_Logger.log("placeOrder",  f" addition info : orderId: {self.nextOrderId}", Logger.Level.DETAIL)
        parms = {"contract": contract, "order": order}
        req = self.register_requests(type = REQUEST_TYPES.PLACE_ORDER, reqId = self.nextOrderId,timeout=10, delayWhenFinished=0.1, parms=parms)
        super().placeOrder(self.nextOrderId, contract, order)
        self.nextOrderId += 1
        return req

    def openOrder(self, orderId: int, contract: Contract, order: Order, orderState: OrderState):
        Global_Values.G_Logger.log("openOrder",  f"orderId: {orderId}, contract: {contract}, order: {order}, orderState: {orderState}", Logger.Level.DATA)        
        self.openOrdersData[orderId] = {"contract": contract, "order": order, "orderState": orderState}

        req = self.find_request_by_type_list([REQUEST_TYPES.PLACE_ORDER, REQUEST_TYPES.UPDATE_ORDER,REQUEST_TYPES.GET_OPEN_ORDERS],orderId) 
        if req is not None:
            if len(req.response.contents) == 0:
                req.response.contents.append({"openOrdersData": self.openOrdersData})
            else:
                req.response.contents[0]["openOrdersData"] = self.openOrdersData

    
    def orderBound(self, reqId: int, apiClientId: int, apiOrderId: int):
        Global_Values.G_Logger.log( "orderBound",  f"reqId: {reqId}, apiClientId: {apiClientId}, apiOrderId: {apiOrderId}", Logger.Level.DATA)     

    def getOpenOrders(self):
        result = {}
        for openOrderItem in self.openOrdersData.values():
            if openOrderItem["orderState"].status != "PendingCancel":
                result[openOrderItem["order"].orderId] = openOrderItem
        
        return result
    
    def orderStatus(self, orderId: int, status: str, filled: Decimal, remaining: Decimal, avgFillPrice: float, permId: int, parentId: int, lastFillPrice: float, clientId: int, whyHeld: str, mktCapPrice: float):
        Global_Values.G_Logger.log("orderStatus",  f"orderId: {orderId}, status: {status}, filled: {filled}, remaining: {remaining}, avgFillPrice: {avgFillPrice}, permId: {permId}, parentId: {parentId}, lastFillPrice: {lastFillPrice}, clientId: {clientId}, whyHeld: {whyHeld}, mktCapPrice: {mktCapPrice}", Logger.Level.DATA)
        
        self.openStatusData[orderId] = {"status": status, "filled": int(filled), "remaining": int(remaining), "avgFillPrice": avgFillPrice, "permId": permId, "parentId": parentId, "lastFillPrice": lastFillPrice, "clientId": clientId, "whyHeld": whyHeld, "mktCapPrice": mktCapPrice}
        req = self.find_request_by_type_list([REQUEST_TYPES.PLACE_ORDER,REQUEST_TYPES.UPDATE_ORDER],orderId) 
        if req is not None:
            if len(req.response.contents) == 0:
                req.response.contents.append({"openStatusData": self.openStatusData})
            else:
                req.response.contents[0]["openStatusData"] = self.openStatusData
           
            req.done.set()



    #This will be callback by login successfuly, placeOrder 
    def openOrderEnd(self):
        Global_Values.G_Logger.log("openOrderEnd", "", Logger.Level.DETAIL)
        
        req = self.find_request_by_type_list([REQUEST_TYPES.GET_OPEN_ORDERS])
        if req is not None:
            req.response.contents.append(self.openOrdersData)
            req.done.set()

        


    #dont has call back, delete and place new order better.
    @RequestWarper
    def updateOrder(self, orderId: int, contract: Contract, order: Order):
        req = self.register_requests(type = REQUEST_TYPES.UPDATE_ORDER, reqId = orderId, timeout=5, delayWhenFinished=0.1)
        super().placeOrder(orderId, contract, order)
        return req
    
    # dont use warpper, because sub-function used
    def replaceOder(self, orderId: int, contract: Contract, order: Order):
    
        Global_Values.G_Logger.log("replaceOder",  f"orderId: {orderId}, contract: {contract}, order: {order}", Logger.Level.DETAIL)
        

        req =self.cancelOrder(orderId)
        req.done.delay_wait()  

        if len(req.response.errors) > 0:
            return req
        
        req2 = self.placeOrder(contract, order)
        return req2    


    
    @RequestWarper
    def cancelOrder(self, orderId: int ):
        cancelOrder = OrderCancel()
        cancelOrder.manualOrderCancelTime = ""

        req =self.register_requests(type = REQUEST_TYPES.CANCEL_ORDER, reqId = orderId, timeout=10, delayWhenFinished=0.1)
        super().cancelOrder(orderId, cancelOrder)
        return req

    @RequestWarper
    def reqAllOpenOrders(self):
        req = self.register_requests(REQUEST_TYPES.GET_OPEN_ORDERS, timeout=10, delayWhenFinished=0.1)
        super().reqAllOpenOrders()
        return req


        
    def error(self, tickerId: TickerId, errorCode: int, errorString: str , contract: Any = None):
        isUseLessMsg = False
        for _msg in  MESSAGE_USELESS.MESSAGES:
            if errorString.find(_msg) != -1:
                isUseLessMsg = True
                break
        if not isUseLessMsg:
            Global_Values.G_Logger.log("error",  f"reqId: {tickerId}, errorCode: {errorCode}, errorString: {errorString} contract:{contract}", Logger.Level.DETAIL)

            
        msg:dict = {"errorCode": errorCode, "errorString": errorString}

        req = None
        if tickerId != -1:
            req = self.find_request_by_reqId(tickerId) 
    
        elif errorCode in WARNING_CANCEL_UPDATEACCOUNT.MESSAGES:
            req = self.find_request(REQUEST_TYPES.CANCEL_UPDATE_ACCOUNT) 
            if req is not None:
                req.keepAlive = False

        elif errorCode in ERROR_CONNECTION.MESSAGES:
            req = self.find_request(REQUEST_TYPES.CONNECT) 
            self.TWS_connection = False        
            self.accountUpdateTime = ""

        
     
        if req is not None:
            if errorCode in MESSAGE_INPUT_ERROR.MESSAGES:
                req.keepAlive = False

            if errorCode in WARNING.MESSAGES:
                req.response.warnings.append(msg)
                 
            elif self.patternMessageDone.search(errorString):
           
                req.response.infos.append(msg)
            else:
                req.response.errors.append(msg)
                req.keepAlive = False
            
            # print( f"error req {req} type: {req.type} id : {req.reqId}  error{ req.response.errors}")
            req.done.set()
            return


        if errorCode in INFO_TWS_AVILABLE.SUCCESS_MESSAGES:
            self.TWS_aviliable = True
        
        elif errorCode in INFO_TWS_AVILABLE.FAIL_MESSAGES:
            self.TWS_aviliable = False



   # the reqId is refer to orderId , not request Id, so can't remove request by reqId
    @RequestWarper
    def reqExecutions(self, reqId: int, filter: ExecutionFilter):
        self.execDetailsData = {}
        req = self.register_requests(type =REQUEST_TYPES.GET_EXECUTIONS, timeout=10)
        super().reqExecutions( reqId, filter)
        return req
    
    #it will also be exec when order is filled
    def execDetails(self, reqId, contract, execution):
        Global_Values.G_Logger.log("execDetails",  f"reqId: {reqId}, contract: {contract}, execution: {execution}", Logger.Level.DATA)

        if  execution.execId not in self.execDetailsData:
            self.execDetailsData[execution.execId] = {}

        self.execDetailsData[execution.execId]["contract"] = contract
        self.execDetailsData[execution.execId]["execution"] = execution
   
        req = self.find_request(REQUEST_TYPES.GET_EXECUTIONS)
       
        if req is not None:
            req.response.contents.append(self.execDetailsData)
       



    def commissionReport(self, commissionReport: CommissionReport):
        Global_Values.G_Logger.log("commissionReport",  f"commissionReport: {commissionReport}", Logger.Level.DATA)
        
        if commissionReport.execId not in self.execDetailsData:
            self.execDetailsData[commissionReport.execId] = {}
            
        self.execDetailsData[commissionReport.execId]["commissionReport"] = commissionReport

        req = self.find_request(REQUEST_TYPES.GET_EXECUTIONS)
        if req is not None:
            req.response.contents.append(self.execDetailsData)
  

  
    def execDetailsEnd(self, reqId: int):
        Global_Values.G_Logger.log("execDetailsEnd",  f"reqId: {reqId}", Logger.Level.DETAIL)

        req = self.find_request(REQUEST_TYPES.GET_EXECUTIONS)
        if req is not None:
            req.done.set()
        else:
            raise Exception("execDetailsEnd without request")
        
    


    @RequestWarper
    def reqHistoricalData(self, reqId : int, contract: Contract, endDateTime: str, durationStr: str, barSizeSetting: str, whatToShow: str, useRTH: int, formatDate: int, keepUpToDate: bool, chartOptions: ListOfTagValue):
        timeout = 10 if keepUpToDate  else 0
        parms = {"contract": contract, "endDateTime": endDateTime, "durationStr": durationStr, "barSizeSetting": barSizeSetting, "whatToShow": whatToShow, "useRTH": useRTH, "formatDate": formatDate, "keepUpToDate": keepUpToDate, "chartOptions": chartOptions}
        req = self.register_requests(type = REQUEST_TYPES.GET_HISTORICAL_DATA,  reqId = reqId, keepAlive=keepUpToDate, timeout=timeout, parms=parms)
        super().reqHistoricalData(req.reqId, contract, endDateTime, durationStr, barSizeSetting, whatToShow, useRTH, formatDate, keepUpToDate, chartOptions)
        return req


    def historicalData(self, reqId: int, bar: BarData):
        Global_Values.G_Logger.log("historicalData",  f"reqId: {reqId}, bar: {bar}", Logger.Level.DATA)

        req = self.reqIDMapping.get(reqId)
        if req is not None:

            newBar = {"date": bar.date, "open": bar.open, "high": bar.high, "low": bar.low, "close": bar.close, "volume": int(bar.volume), "wap": int(bar.wap), "barCount": bar.barCount}
            req.response.contents.append(newBar) # type: ignore

        else:
            raise Exception("historicalDataEnd without request")
        

    def historicalDataUpdate(self, reqId: int, bar: BarData):
        Global_Values.G_Logger.log("historicalDataUpdate",  f"reqId: {reqId}, bar: {bar}", Logger.Level.UPDATED_DATA)

        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_DATA],reqId)
        
        if req is not None:
           
            newBar = {"date": bar.date, "open": bar.open, "high": bar.high, "low": bar.low, "close": bar.close, "volume": int(bar.volume), "wap": int(bar.wap), "barCount": bar.barCount}
            bars = self.barsData[reqId]

            if  len(bars)>0 and newBar["date"] == bars.get_data(-1)["date"]:
                bars.set_data(-1, newBar)
                req.response.contents[-1] = newBar # type: ignore
            else:
                bars.add_data(newBar)
                req.response.contents.append(newBar) # type: ignore


            if req.update_callback is not None:
                req.update_callback(req, newBar)
        else:
            raise Exception("historicalDataEnd without request")
        

    def historicalDataEnd(self, reqId: int, start: str, end: str):
        Global_Values.G_Logger.log("historicalDataEnd",  f"reqId: {reqId}, start: {start}, end: {end}", Logger.Level.DETAIL)
       
        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_DATA],reqId)
        
        if req is not None:
            self.barsData[reqId] = BufferList(10000,req.response.contents) 
            req.done.set()
        else:
            raise Exception("historicalDataEnd without request")

    @RequestWarper
    def cancelHistoricalData(self, reqId):
        delReq = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_DATA,  REQUEST_TYPES.GET_HISTORICAL_TICKS],reqId)
        if delReq is not None:
            delReq.pendingDelete = True
       
        req = self.register_requests( type = REQUEST_TYPES.CANCEL_HISTORICAL_DATA, reqId = reqId, timeout=2)
        super().cancelHistoricalData(reqId)
        return req



    @RequestWarper
    def reqRealTimeBars(self, reqId, contract, barSize, whatToShow, useRTH, realTimeBarsOptions):
        req = self.register_requests(type = REQUEST_TYPES.GET_REAL_TIME_BARS, reqId = reqId, keepAlive=True, timeout=2)
        super().reqRealTimeBars(reqId, contract, barSize, whatToShow, useRTH, realTimeBarsOptions)
        return req


    def realTimeBar(self, reqId: int, time: int, open_: float, high: float, low: float, close: float, volume: int, wap: float, count: int):
        Global_Values.G_Logger.log("realTimeBar",  f"reqId: {reqId}, time: {time}, open_: {open_}, high: {high}, low: {low}, close: {close}, volume: {volume}, wap: {wap}, count: {count}", Logger.Level.DATA)
      
        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_REAL_TIME_BARS],reqId)
        req.response.contents.append({"time": time, "open": open_, "high": high, "low": low, "close": close, "volume": volume, "wap": wap, "count": count}) # type: ignore

    def realtimeBarEnd(self, reqId: int):
        Global_Values.G_Logger.log("realtimeBarEnd",  f"reqId: {reqId}", Logger.Level.DETAIL)
 
        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_REAL_TIME_BARS],reqId)
        if req is not None:
            req.done.set()
        else:
            raise Exception("realtimeBarEnd without request") 

    @RequestWarper
    def cancelRealTimeBars(self, reqId):
       

        delReq = self.find_request_by_type_list( [REQUEST_TYPES.GET_REAL_TIME_BARS],reqId)
        if delReq is not None:
            delReq.pendingDelete = True

        req = self.register_requests(type = REQUEST_TYPES.CANCEL_REAL_TIME_BARS, reqId = reqId, timeout=2)
        
        super().cancelRealTimeBars(reqId)
        return req



    @RequestWarper
    def reqMktData(self, reqId: int, contract: Contract, genericTickList: str, snapshot: bool, regulatorySnapshot: bool, mktDataOptions: ListOfTagValue):
        parms = {"contract": contract, "genericTickList": genericTickList, "snapshot": snapshot, "regulatorySnapshot": regulatorySnapshot, "mktDataOptions": mktDataOptions}
        req = self.register_requests(type = REQUEST_TYPES.GET_MARKET_DATA, reqId = reqId, keepAlive=True , timeout=10,  parms=parms)
        super().reqMktData( reqId, contract, genericTickList, snapshot, regulatorySnapshot, mktDataOptions)
        return req

    def tickPrice(self, reqId: int, tickType: int, price: float, attrib: TickAttrib):
        Global_Values.G_Logger.log("tickPrice",  f"reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, price: {price}, attrib: {attrib}", Logger.Level.UPDATED_DATA)


        req = self.reqIDMapping.get(reqId)
   
        if req is not None:
            # if not req.done.is_set():
            #     req.done.set()
                
            if req.update_callback is not None:
                req.update_callback(req, {"tickType": tickType, "price": price, "attrib": attrib})
 
        
    
    def tickSize(self, reqId: int, tickType: int, size: int):
        Global_Values.G_Logger.log("tickSize",  f"reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, size: {size}", Logger.Level.UPDATED_DATA)

   


    def tickString(self, reqId: int, tickType: int, value: str):
        Global_Values.G_Logger.log("tickString",  f"reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, value: {value}", Logger.Level.UPDATED_DATA)

    
    def tickGeneric(self, reqId: int, tickType: int, value: float):
        Global_Values.G_Logger.log("tickGeneric",  f"reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, value: {value}", Logger.Level.UPDATED_DATA)
       


    def tickEFP(self, reqId: int, tickType: int, basisPoints: float, formattedBasisPoints: str,
                impliedFuture: float, holdDays: int, futureExpiry: str, dividendImpact: float,
                dividendsToExpiry: float):
        Global_Values.G_Logger.log("tickEFP",  f"reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, basisPoints: {basisPoints}, formattedBasisPoints: {formattedBasisPoints}, impliedFuture: {impliedFuture}, holdDays: {holdDays}, futureExpiry: {futureExpiry}, dividendImpact: {dividendImpact}, dividendsToExpiry: {dividendsToExpiry}", Logger.Level.UPDATED_DATA)
        

    def tickSnapshotEnd(self, reqId: int):
        Global_Values.G_Logger.log("tickSnapshotEnd",  f"reqId: {reqId}", Logger.Level.DETAIL)

        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_MARKET_DATA],reqId)
        if req is not None:
            req.done.set()
        else:
            raise Exception("tickSnapshotEnd without request") 


    def tickReqParams(self, reqId, minTick, bboExchange, snapshotPermissions):
        Global_Values.G_Logger.log("tickReqParams",  f"reqId: {reqId},  minTick: {minTick}, bboExchange: {bboExchange}, snapshotPermissions: {snapshotPermissions}", Logger.Level.DETAIL)

       



    def tickOptionComputation(self, reqId, tickType, tickAttrib, impliedVol, delta, optPrice, pvDividend, gamma, vega, theta, undPrice):
        Global_Values.G_Logger.log("tickOptionComputation",  f"reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, tickAttrib: {tickAttrib}, impliedVol: {impliedVol}, delta: {delta}, optPrice: {optPrice}, pvDividend: {pvDividend}, gamma: {gamma}, vega: {vega}, theta: {theta}, undPrice: {undPrice}", Logger.Level.DATA)

    
    @RequestWarper
    def reqMarketDataType(self, marketDataType):
        req = self.register_requests(type = REQUEST_TYPES.GET_MARKET_DATA_TYPE, timeout=0.5 , noResponse=True)
        super().reqMarketDataType(marketDataType)
        return req


    #it will call back after reqMktData
    def marketDataType(self, reqId: int, marketDataType: int):
        Global_Values.G_Logger.log("marketDataType",  f"reqId: {reqId}, marketDataType: {MarketDataTypeEnum.toStr(marketDataType)}", Logger.Level.DETAIL)

        # when reqMrktData is called, it will call back marketDataType
        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_MARKET_DATA],reqId)
       
        if req is not None:
            # print(f"marketDataType req {req} {req.type} id {req.reqId}")
            req.done.set()
        else:
            raise Exception("marketDataType without request")



    @RequestWarper
    def cancelMktData(self, reqId):
        # self.unregister_requests(REQUEST_TYPES.GET_MARKET_DATA, reqId)
        delReq = self.find_request_by_type_list( [REQUEST_TYPES.GET_MARKET_DATA],reqId)
        if delReq is not None:
            delReq.pendingDelete = True
       
        req = self.register_requests( type = REQUEST_TYPES.CANCEL_MARKET_DATA, reqId = reqId, timeout=2)
        super().cancelMktData(reqId)
        return req


    @RequestWarper
    def reqHistoricalTicks(self, reqId : int, contract  : Contract, startDateTime : str, endDateTime : str, nTicks : int, whatToShow : str, useRTH : int, ignoreSize  : bool, miscOptions : ListOfTagValue):
        req = self.register_requests(type = REQUEST_TYPES.GET_HISTORICAL_TICKS, reqId = reqId, delayWhenFinished=0.1)
        super().reqHistoricalTicks(reqId, contract, startDateTime, endDateTime, nTicks, whatToShow, useRTH, ignoreSize, miscOptions)
        return req
    

    def historicalTicks(self, reqId: int, ticks: ListOfHistoricalTick, done: bool):
        Global_Values.G_Logger.log("historicalTicks",  f"reqId: {reqId}, ticks: {ticks}, done: {done}", Logger.Level.DATA)
      

        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_TICKS],reqId)
        req.response.contents.append(ticks) # type: ignore

    def historicalTicksLast(self, reqId: int, ticks: ListOfHistoricalTickLast, done: bool):
        Global_Values.G_Logger.log("historicalTicksLast",  f"reqId: {reqId}, ticks: {ticks}, done: {done}", Logger.Level.DATA)

        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_TICKS],reqId)
        req.response.contents.append(ticks) # type: ignore

        if req is not None:
            req.done.set()
        else:
            raise Exception("historicalTicksLast without request") 



    @RequestWarper
    def reqTickByTickData(self, reqId, contract, tickType, numberOfTicks, ignoreSize):
        req = self.register_requests(type = REQUEST_TYPES.GET_TICK_BY_TICK_DATA, reqId = reqId, keepAlive=True)
        super().reqTickByTickData(reqId, contract, tickType, numberOfTicks, ignoreSize)
        return req


    def tickByTickAllLast(self, reqId: int, tickType: int, time: int, price: float,
                          size: int, tickAttribLast: TickAttribLast,
                          exchange: str, specialConditions: str):
        Global_Values.G_Logger.log("tickByTickAllLast",  f"reqId: {reqId}, tickType: {tickType}, time: {time}, price: {price}, size: {size}, tickAttribLast: {tickAttribLast}, exchange: {exchange}, specialConditions: {specialConditions}", Logger.Level.DATA)
         
        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_TICK_BY_TICK_DATA],reqId)
        req.response.contents.append({"tickType": tickType, "time": time, "price": price, "size": size, "tickAttribLast": tickAttribLast, "exchange": exchange, "specialConditions": specialConditions}) # type: ignore

    
    @RequestWarper
    def cancelTickByTickData(self, reqId):

        delReq = self.find_request_by_type_list( [REQUEST_TYPES.GET_TICK_BY_TICK_DATA],reqId)
        if delReq is not None:
            delReq.pendingDelete = True


        req = self.register_requests(type = REQUEST_TYPES.CANCEL_TICK_BY_TICK_DATA, reqId = reqId, timeout=2)
        super().cancelTickByTickData(reqId)
        return req


    @RequestWarper
    def reqMktDepth(self, reqId, contract, numRows, isSmartDepth, mktDepthOptions=[]):
        req = self.register_requests( type = REQUEST_TYPES.GET_MARKET_DEPTH, reqId = reqId, keepAlive=True)
        super().reqMktDepth(reqId, contract, numRows, isSmartDepth, mktDepthOptions)
        return req
    
    def updateMktDepth(self, reqId: int, position: int, operation: int,
                        side: int, price: float, size: int):
        Global_Values.G_Logger.log("updateMktDepth",  f"reqId: {reqId}, position: {position}, operation: {operation}, side: {side}, price: {price}, size: {size}", Logger.Level.UPDATED_DATA)
        
        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_MARKET_DEPTH],reqId)
        req.response.contents.append({"position": position, "operation": operation, "side": side, "price": price, "size": size}) # type: ignore


    @RequestWarper
    def cancelMktDepth(self, reqId, isSmartDepth=False):
       
        delReq = self.find_request_by_type_list( [REQUEST_TYPES.GET_MARKET_DEPTH],reqId)
        if delReq is not None:
            delReq.pendingDelete = True

        req = self.register_requests( type = REQUEST_TYPES.CANCEL_MARKET_DEPTH, reqId = reqId, timeout=2)
        super().cancelMktDepth(reqId, isSmartDepth)
        return req
    



    @RequestWarper
    def reqAccountUpdates(self, accountCode: str):
        # if self.accountUpdateTime != "":
        #     req = self.cancelAccountUpdates(accountCode)
        #     req.done.delay_wait()


        self.accountData = {}
        self.protfolioData = {}
        self.accountUpdateTime = ""

        req2 = self.register_requests( type = REQUEST_TYPES.UPDATE_ACCOUNT, keepAlive=True )
        super().reqAccountUpdates(True, accountCode)
        return req2

    
    @RequestWarper
    def cancelAccountUpdates(self, accountCode: str):
        # self.unregister_requests(REQUEST_TYPES.UPDATE_ACCOUNT, -1)

        delReq = self.find_request_by_type_list( [REQUEST_TYPES.UPDATE_ACCOUNT],-1)
        if delReq is not None:
            delReq.pendingDelete = True
        
        req2 = self.register_requests( type = REQUEST_TYPES.CANCEL_UPDATE_ACCOUNT, timeout=2 , noResponse=True)
        super().reqAccountUpdates(False,accountCode)
        
        return req2

    
    def updatePortfolio(self, contract: Contract, position: float,
                        marketPrice: float, marketValue: float,
                        averageCost: float, unrealizedPNL: float,
                        realizedPNL: float, accountName: str):
        Global_Values.G_Logger.log("updatePortfolio",  f"contract: {contract}, position: {position}, marketPrice: {marketPrice}, marketValue: {marketValue}, averageCost: {averageCost}, unrealizedPNL: {unrealizedPNL}, realizedPNL: {realizedPNL}, accountName: {accountName}", Logger.Level.DATA)
        
        if accountName not in self.protfolioData:
            self.protfolioData[accountName] = {}
        
        self.protfolioData[accountName][contract.conId] = {'contract': contract, 'position': position, 'marketPrice': marketPrice, 'marketValue': marketValue, 'averageCost': averageCost, 'unrealizedPNL': unrealizedPNL, 'realizedPNL': realizedPNL, 'accountName': accountName}
         
        

    def updateAccountValue(self, key: str, val: str, currency: str,
                          accountName: str):
        
        Global_Values.G_Logger.log("updateAccountValue",  f"key: {key}, val: {val}, currency: {currency}, accountName: {accountName}", Logger.Level.DATA)
        

        if accountName not in self.accountData:
            self.accountData[accountName] = {}
           
        try:
            new_val = float(val)
        except ValueError:
            new_val = val

        self.accountData[accountName][(key , currency)] = {'val': new_val, 'currency': currency, 'accountName': accountName}
       


    def findPortfolio(self, contract: Contract):
        for accountName, accountData in self.protfolioData.items():
            for conId, portfolio in accountData.items():
                if (
                    portfolio['contract'].symbol == contract.symbol and
                    portfolio['contract'].secType == contract.secType and 
                    portfolio['contract'].exchange == contract.exchange and 
                    portfolio['contract'].currency == contract.currency  
                ):
                    return portfolio
        return None
   

    def updateAccountTime(self, timeStamp: str):
        Global_Values.G_Logger.log("updateAccountTime",  f"timeStamp: {timeStamp}", Logger.Level.DATA)

        self.accountUpdateTime = timeStamp

 


    def accountDownloadEnd(self, accountName: str):
        Global_Values.G_Logger.log("accountDownloadEnd",  f"accountName: {accountName}", Logger.Level.DETAIL)
        
       
        req = self.find_request_by_type_list([REQUEST_TYPES.UPDATE_ACCOUNT])
        if req is not None:
            self.accountName  = list(self.accountData.keys())[0]
            req.response.contents.append({"accountData": self.accountData, "protfolioData": self.protfolioData, "accountUpdateTime": self.accountUpdateTime})
            req.done.set()
        else:
            raise Exception("accountDownloadEnd without request")
        

    @RequestWarper
    def reqAccountSummary(self, reqId: int, groupName: str, tags: str):
        # req = self.find_request(REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY, reqId) 
        # if req is not None:
        #     req2 = self.cancelAccountSummary(req.reqId)
        #     req2.done.delay_wait()

        self.accountSummaryData = {}

        req3 = self.register_requests( type = REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY, reqId = reqId, timeout=10, keepAlive=True)
        super().reqAccountSummary(reqId, groupName, tags)
        return req3


    def accountSummary(self, reqId: int, account: str, tag: str, value: str,
                      currency: str):
        Global_Values.G_Logger.log("accountSummary",  f"reqId: {reqId}, account: {account}, tag: {tag}, value: {value}, currency: {currency}", Logger.Level.DATA)
       

        if account not in self.accountSummaryData:
            self.accountSummaryData[account] = {}
        
        self.accountSummaryData[account][tag] = {'value': value, 'currency': currency}

        req = self.find_request_by_type_list([REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY],reqId)
        if req is not None:
            req.response.contents.append(self.accountSummaryData)
        else:
            raise Exception("accountSummary without request")



    def accountSummaryEnd(self, reqId: int):
        Global_Values.G_Logger.log("accountSummaryEnd",  f"reqId: {reqId}", Logger.Level.DETAIL)
  

        req = self.find_request_by_type_list([REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY],reqId)
        if req is not None:
            req.done.set()
        else:
            raise Exception("accountSummaryEnd without request")


    @RequestWarper
    def cancelAccountSummary(self, reqId: int):
        #self.unregister_requests(REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY, reqId)
       
        delReq = self.find_request_by_type_list( [REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY],reqId)
        if delReq is not None:
            delReq.pendingDelete = True

     
        req2 = self.register_requests( type = REQUEST_TYPES.CANCEL_ACCOUNT_SUMMARY, reqId = reqId, timeout=2 , noResponse=True)
        super().cancelAccountSummary(reqId)
        return req2





    @RequestWarper
    def reqPositions(self):
        req = self.register_requests( type = REQUEST_TYPES.GET_POSITIONS, keepAlive=True)
        super().reqPositions()
        return req
    

    def position(self, account: str, contract: Contract, pos: float,
                avgCost: float):
        Global_Values.G_Logger.log("position",  f"account: {account}, contract: {contract}, pos: {pos}, avgCost: {avgCost}", Logger.Level.DATA)
      

        
    def positionEnd(self):
        Global_Values.G_Logger.log("positionEnd",  "", Logger.Level.DETAIL)
       
        req = self.find_request_by_type_list([REQUEST_TYPES.GET_POSITIONS])
        if req is not None:
            req.done.set()
        else:
            raise Exception("positionEnd without request")


    @RequestWarper
    def cancelPositions(self):
        # self.unregister_requests(REQUEST_TYPES.GET_POSITIONS, -1)

        delReq = self.find_request_by_type_list( [REQUEST_TYPES.GET_POSITIONS])
        if delReq is not None:
            delReq.pendingDelete = True

        req = self.register_requests( type = REQUEST_TYPES.CANCEL_POSITIONS, timeout=2)
        super().cancelPositions()
        return req


    def nextValidId(self, orderId: int):
        Global_Values.G_Logger.log("nextValidId",  f"orderId: {orderId}", Logger.Level.DETAIL)

        if orderId <= TickerID_RULES.REQUEST_ID_MAX:
            self.nextOrderId = TickerID_RULES.REQUEST_ID_MAX + 1
        else:
            self.nextOrderId = orderId
      
        req = self.find_request_by_type_list([REQUEST_TYPES.PLACE_ORDER],orderId)
        if req is not None:
            req.done.set()

        req2 = self.find_request_by_type_list([REQUEST_TYPES.CONNECT, REQUEST_TYPES.GET_IDS])
        if req2 is not None:
            req2.done.set()

        
    

    @RequestWarper
    def reqIds(self):
        req = self.register_requests( type = REQUEST_TYPES.GET_IDS, timeout=5)
        super().reqIds(0)
        return req


    @RequestWarper
    def reqCompletedOrders(self, apiOnly):
        req = self.register_requests( type = REQUEST_TYPES.GET_COMPLETED_ORDERS)
        super().reqCompletedOrders(apiOnly)
        return req

    def completedOrders(self, contract: Contract, order: Order, orderState: OrderState):
        Global_Values.G_Logger.log("completedOrders",  f"contract: {contract}, order: {order}, orderState: {orderState}", Logger.Level.DATA)

        self.completedOrdersData[order.orderId] =  {"contract": contract, "order": order, "orderState": orderState}

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_COMPLETED_ORDERS])
        if req is not None:
            req.response.contents.append(self.completedOrdersData)
      
        else:
            raise Exception("completedOrders without request")


    def completedOrdersEnd(self):
        Global_Values.G_Logger.log("completedOrdersEnd",  "", Logger.Level.DETAIL)
   
        req = self.find_request_by_type_list([REQUEST_TYPES.GET_COMPLETED_ORDERS])
        if req is not None:
            req.done.set()
        else:
            raise Exception("completedOrdersEnd without request")


    @RequestWarper
    def reqCurrentTime(self):
        req = self.register_requests( type = REQUEST_TYPES.GET_CURRENT_TIME, timeout=5)
        super().reqCurrentTime()
        return req


    def currentTime(self, time: int):
        Global_Values.G_Logger.log("currentTime",  f"time: {time}", Logger.Level.DETAIL)
       
        self.sys_currentTime = time

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_CURRENT_TIME])
        if req is not None:
            req.response.contents.append(time)
            req.done.set()
        else:
            raise Exception("currentTime without request")
        

    @RequestWarper
    def reqFundamentalData(self, reqId, contract, reportType, fundamentalDataOptions):
        req = self.register_requests( type = REQUEST_TYPES.GET_FUNDAMENTAL_DATA, reqId = reqId, timeout=10)
        super().reqFundamentalData(reqId, contract, reportType, fundamentalDataOptions)
        return req
    
    def fundamentalData(self, reqId: int, data: str):
        Global_Values.G_Logger.log("fundamentalData",  f"reqId: {reqId}, data: {data}", Logger.Level.DATA)

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_FUNDAMENTAL_DATA],reqId)
        if req is not None:
            req.response.contents.append(data)
        else:
            raise Exception("fundamentalData without request")
        

    @RequestWarper
    def cancelFundamentalData(self, reqId: int):
        # self.unregister_requests(REQUEST_TYPES.GET_FUNDAMENTAL_DATA, reqId)
        delReq = self.find_request_by_type_list( [REQUEST_TYPES.GET_FUNDAMENTAL_DATA],reqId)
        if delReq is not None:
            delReq.pendingDelete = True

        req = self.register_requests( type = REQUEST_TYPES.CANCEL_FUNDAMENTAL_DATA, reqId = reqId, timeout=10)
        super().cancelFundamentalData(reqId)
        return req
    


    @RequestWarper
    def reqContractDetails(self, reqId: int, contract: Contract):
        req = self.register_requests( type = REQUEST_TYPES.GET_CONTRACT_DETAILS, reqId = reqId, timeout=5)
        super().reqContractDetails(reqId, contract)
        return req

    def contractDetails(self, reqId: int, contractDetails: ContractDetails):
        Global_Values.G_Logger.log("contractDetails",  f"reqId: {reqId}, contractDetails: {contractDetails}", Logger.Level.DETAIL)
       
        req = self.find_request_by_type_list([REQUEST_TYPES.GET_CONTRACT_DETAILS],reqId)
        if req is not None:
            req.response.contents.append(contractDetails)
        else:
            raise Exception("contractDetails without request")
        
    def bondContractDetails(self, reqId: int, contractDetails: ContractDetails):
        Global_Values.G_Logger.log("bondContractDetails",  f"reqId: {reqId}, contractDetails: {contractDetails}", Logger.Level.DATA)
  
        
        req = self.find_request_by_type_list([REQUEST_TYPES.GET_CONTRACT_DETAILS],reqId)
        if req is not None:
            req.response.contents.append(contractDetails)
        else:
            raise Exception("bondContractDetails without request")

    def contractDetailsEnd(self, reqId: int):
        Global_Values.G_Logger.log("contractDetailsEnd",  f"reqId: {reqId}", Logger.Level.DETAIL)
       

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_CONTRACT_DETAILS],reqId)
        if req is not None:
            req.done.set()
        else:
            raise Exception("contractDetailsEnd without request")
        

    @RequestWarper
    def reqWshEventData(self, reqId: int, wshEventData: WshEventData, MIN_SERVER_VER_WSH_EVENT_DATA_FILTERS_DATE: int):
        req = self.register_requests( type = REQUEST_TYPES.GET_WSH_EVENT_DATA, reqId = reqId, timeout=5)
        super().reqWshEventData(reqId, wshEventData, MIN_SERVER_VER_WSH_EVENT_DATA_FILTERS_DATE)
        return req
    
    def wshEventData(self, reqId: int, wshEventData: WshEventData):
        Global_Values.G_Logger.log("wshEventData",  f"reqId: {reqId}, wshEventData: {wshEventData}", Logger.Level.DATA)
    

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_WSH_EVENT_DATA],reqId)
        if req is not None:
            req.response.contents.append(wshEventData)
        else:
            raise Exception("wshEventData without request")
        

    @RequestWarper
    def cancelWshEventData(self, reqId: int):
        # self.unregister_requests(REQUEST_TYPES.GET_WSH_EVENT_DATA, reqId)

        delReq = self.find_request_by_type_list( [REQUEST_TYPES.GET_WSH_EVENT_DATA],reqId)
        if delReq is not None:
            delReq.pendingDelete = True

        req = self.register_requests( type = REQUEST_TYPES.CANCEL_WSH_EVENT_DATA, reqId = reqId, timeout=5)
        super().cancelWshEventData(reqId)
        return req



    @RequestWarper
    def reqWshMetaData(self, reqId: int):
        req = self.register_requests( type = REQUEST_TYPES.GET_WSH_META_DATA, reqId = reqId, timeout=5)
        super().reqWshMetaData(reqId)
        return req
    
    def wshMetaData(self, reqId: int, wshMetaData: str):
        Global_Values.G_Logger.log("wshMetaData",  f"reqId: {reqId}, wshMetaData: {wshMetaData}", Logger.Level.DATA)
    

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_WSH_META_DATA],reqId)
        if req is not None:
            req.response.contents.append(wshMetaData)
        else:
            raise Exception("wshMetaData without request")
        

    @RequestWarper
    def cancelWshMetaData(self, reqId: int):
        # self.unregister_requests(REQUEST_TYPES.GET_WSH_META_DATA, reqId)
        delReq = self.find_request_by_type_list( [REQUEST_TYPES.GET_WSH_META_DATA],reqId)
        if delReq is not None:
            delReq.pendingDelete = True

        req = self.register_requests( type = REQUEST_TYPES.CANCEL_WSH_META_DATA, reqId = reqId, timeout=5)
        super().cancelWshMetaData(reqId)
        return req


    @RequestWarper
    def reqHeadTimeStamp(self, reqId: TickerId, contract: Contract, whatToShow: str, useRTH: TickerId, formatDate: TickerId):
        req = self.register_requests( type = REQUEST_TYPES.GET_HEAD_TIME_STAMP, reqId = reqId, timeout=5, keepAlive=True)
        super().reqHeadTimeStamp(reqId, contract, whatToShow, useRTH, formatDate)
        return req

    def cancelHeadTimeStamp(self, reqId: int):
        # self.unregister_requests(REQUEST_TYPES.GET_HEAD_TIME_STAMP, reqId)

        delReq = self.find_request_by_type_list( [REQUEST_TYPES.GET_HEAD_TIME_STAMP],reqId)
        if delReq is not None:
            delReq.pendingDelete = True

        req = self.register_requests(REQUEST_TYPES.CANCEL_HEAD_TIME_STAMP, reqId, timeout=5)
        super().cancelHeadTimeStamp(reqId)
        return req

    def headTimestamp(self, reqId: int, headTimestamp: str):
        Global_Values.G_Logger.log("headTimestamp",  f"reqId: {reqId}, headTimestamp: {headTimestamp}", Logger.Level.DATA)

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_HEAD_TIME_STAMP],reqId)
        if req is not None:
            req.response.contents.append(headTimestamp)
        else:
            raise Exception("headTimestamp without request")

    def connect(self, host, port, clientId):
        req = self.register_requests(REQUEST_TYPES.CONNECT, timeout=10 ,delayWhenFinished=0.1)
        super().connect(host, port, clientId)
        return req


    def reconnect(self):
        attempt = 0
        isConnect  = False
        self.TWS_connection = True
        while isConnect == False and attempt < 10:
            Global_Values.G_Logger.log("IBTool",  "Attempt Connect to TWS...", Logger.Level.GENERAL)

            super().disconnect()
            time.sleep(1)

            cfg_connection =  self.config['Connection']
            req = self.connect(cfg_connection['host'], cfg_connection['port'], cfg_connection['clientId'])

           
            thread_run= Thread(target=super().run,args=(), daemon=True, name="IB App Run")
            thread_run.start()            

            req.done.delay_wait()
     
            isConnect = super().isConnected()
            self.TWS_connection = isConnect

            attempt += 1
            Global_Values.G_Logger.log("IBTool",  f"Connection attempt {attempt} : {['Not Success', 'Success'][bool(isConnect)]}", Logger.Level.GENERAL)
            


    def is_account_data_ready(self):
        result =  True

        if self.accountUpdateTime == "":
            result = False
        else:
    
            # 切分出小时和分钟
            h, m = map(int, self.accountUpdateTime.split(':'))

            # 用同一次调用获得当前洛杉矶时间和今天日期
       
            now_dt = datetime.now( self.twsTimeZone )
            today  = now_dt.date()

            # 直接在构造时指定 tzinfo，生成带时区的 account_dt
            account_dt = datetime(
                year = today.year,
                month= today.month,
                day  = today.day,
                hour = h,
                minute=m,
                tzinfo= self.twsTimeZone 
            )

            # 如果拼出来的时间比当前还晚，说明其实是“昨天”的
            if account_dt > now_dt:
                account_dt -= timedelta(days=1)

            # 判断差值是否在 3.6 分钟以内
            result = (now_dt - account_dt) <= timedelta(minutes=3.6)
     
        return result
    

    def calculate_commission(self, contract: Contract, order: Order):
        if contract.secType != "STK" :
            raise Exception("calculate_commission only support Stock right now, modify the code")
        
        if not(contract.currency == "USD" or contract.currency == "JPY"):
            raise Exception("calculate_commission only support USD and JPY right now, modify the code")

        commission = 0.0
    
        commission_type = self.config["Commission"]["SelectedType"]
        commissionData = self.config["Commission"][commission_type][contract.currency][contract.secType]

        if  commissionData.get("CommissionPerShare")  is not None:
            commission = commissionData["CommissionPerShare"] * float(order.totalQuantity)

        elif commissionData.get("CommissionPercentPerTrade") is not None:
            commission = order.lmtPrice *  float(order.totalQuantity) * (commissionData["CommissionPercentPerTrade"] / 100.0)
        else:
            raise Exception("calculate_commission: Commission type not supported")

        if commission  <   commissionData["CommissionMin"]:
            commission = commissionData["CommissionMin"]

        return commission



    def get_message_done_pattern(self):
        escaped_values = [re.escape(msg) for msg in MESSAGE_DONE.MESSAGES.values()]
        pattern_str = r"(?:%s)" % "|".join(escaped_values)
        return re.compile(pattern_str)
