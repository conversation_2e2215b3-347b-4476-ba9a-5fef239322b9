from typing import Dict, Any, List, Optional
from BufferList import BufferList
import numpy as np

def sma(
    data: "BufferList[Dict[str, Any]]",   # ⬅️ payload is dict-ish
    *,
    field: str,
    length: int = 20,
    offset: int = 0,
) -> List[Optional[float]]:
    if length <= 0:
        raise ValueError("length must be positive")
    if len(data) == 0:
        return []

    # pull out the column
    try:
        column = [row[field] for row in data]
    except KeyError as e:
        raise KeyError(f"missing field {field!r}") from e

    try:
        arr = np.asarray(column, dtype=float)
    except ValueError as e:
        raise ValueError("all values must be castable to float") from e

    csum = np.cumsum(arr, dtype=float)
    csum[length:] -= csum[:-length]
    sma = csum / length

    head = [None] * (length - 1)
    body = sma[length - 1:]

    if offset:
        body = np.roll(body, offset)
        if offset > 0:
            body[:offset] = np.nan
        else:
            body[offset:] = np.nan

    out = head + body.tolist()
    return [None if (isinstance(x, float) and np.isnan(x)) else x for x in out]

