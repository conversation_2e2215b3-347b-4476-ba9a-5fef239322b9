import pandas as pd
import numpy as np
import pandas_market_calendars as mcal
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from pathlib import Path
import json

# -----------------------------------------------------------------------------
# 1. SCALE DOWN A SINGLE BAR
# -----------------------------------------------------------------------------
def scale_down_bar(bar: dict,
                   start_time: pd.Timestamp,
                   end_time: pd.Timestamp,
                   freq: str,
                   high_frac: float = 0.25,
                   low_frac: float = 0.75) -> pd.DataFrame:
    # 1) 构建关键时间点和价格
    timeline = pd.to_datetime([
        start_time,
        start_time + (end_time - start_time) * high_frac,
        start_time + (end_time - start_time) * low_frac,
        end_time
    ])
    prices = [bar["Open"], bar["High"], bar["Low"], bar["Close"]]

    # 2) 目标频率的完整时间索引
    sub_index = pd.date_range(start_time, end_time, freq=freq)
    if sub_index[-1] < end_time:
        sub_index = sub_index.append(pd.DatetimeIndex([end_time]))

    # 3) 插值
    curve = (
        pd.Series(prices, index=timeline)
          .sort_index()
          .reindex(timeline.union(sub_index))
          .interpolate(method="time")
          .loc[sub_index]
    )

    # 4) 生成 OHLC 记录
    records = []
    step = len(sub_index) - 1
    for t0, t1 in zip(sub_index[:-1], sub_index[1:]):
        seg = curve[t0:t1]
        if seg.empty:
            continue
        records.append({
            "Date":     t0,
            "Open":     seg.iloc[0],
            "High":     seg.max(),
            "Low":      seg.min(),
            "Close":    seg.iloc[-1],
            "Volume":   bar["Volume"] / step,
            "WAP":      bar["WAP"],
            "BarCount": bar["BarCount"] / step,
        })

    return pd.DataFrame(records).set_index("Date")


# -----------------------------------------------------------------------------
# 2. RESAMPLE BARS
# -----------------------------------------------------------------------------
def resample_bars(df: pd.DataFrame,
                  from_freq: str,
                  to_freq: str,
                  end_time: pd.Timestamp = None,
                  tradingTimeStart: str = "09:30",
                  tradingTimeEnd: str = "16:00") -> pd.DataFrame:
    df = df.copy()
    df.index = pd.to_datetime(df.index)

    if end_time is not None:
        df = df[df.index <= pd.to_datetime(end_time)]

    from_n = pd.Timedelta(from_freq)
    to_n   = pd.Timedelta(to_freq)

    # UPSAMPLE (daily → intraday)
    if to_n < from_n:
        pieces = []
        for ts, row in df.iterrows():
            date      = ts.date()
            bar_start = pd.Timestamp(f"{date} {tradingTimeStart}")
            bar_end   = pd.Timestamp(f"{date} {tradingTimeEnd}")
            pieces.append(scale_down_bar(row.to_dict(), bar_start, bar_end, to_freq))
        return pd.concat(pieces).sort_index()

    # DOWNSAMPLE (intraday → larger)
    ohlc_dict = {
        "Open": "first", "High": "max",
        "Low":   "min",   "Close": "last",
        "Volume":   "sum", "BarCount": "sum"
    }
    df["WAPxV"] = df["WAP"] * df["Volume"]
    agg = df.resample(to_freq).agg(ohlc_dict)
    agg["WAP"] = agg["WAPxV"] / agg["Volume"]
    return agg.drop(columns="WAPxV")


# -----------------------------------------------------------------------------
# 3. PLOT OHLC CANDLES
# -----------------------------------------------------------------------------
def plot_ohlc(ax, df, width, color_up, color_down, alpha=1.0):
    dates = mdates.date2num(df.index.to_pydatetime())
    for date, o, h, l, c in zip(dates,
                                df['Open'], df['High'],
                                df['Low'], df['Close']):
        color = color_up if c >= o else color_down
        ax.vlines(date, l, h, color=color, alpha=alpha)
        left = date - width / 2
        rect = plt.Rectangle((left, min(o, c)),
                             width, abs(c - o),
                             facecolor=color, alpha=alpha)
        ax.add_patch(rect)

    ax.xaxis_date()
    ax.grid(True)


# -----------------------------------------------------------------------------
# EXAMPLE USAGE
# -----------------------------------------------------------------------------
if __name__ == "__main__":
    # --- build sample data ---
    raw = [{
        "Date":      "2025-07-28",
        "Open":      4323,
        "High":      4550,
        "Low":       4123,
        "Close":     4123,
        "Volume":    200,
        "WAP":       4323,
        "BarCount":  2
    }]
    df_day = pd.DataFrame(raw).set_index("Date")
    df_day.index = pd.to_datetime(df_day.index)

    # resample daily → hourly
    df_hour = resample_bars(df_day, from_freq="1D", to_freq="1h")

    # --- plotting ---
    fig, ax = plt.subplots(figsize=(12, 4))

    # plot daily behind
    plot_ohlc(ax, df_day,
              width=0.8,
              color_up="lightgrey",
              color_down="lightgrey",
              alpha=0.7)

    # plot hourly on top
    plot_ohlc(ax, df_hour,
              width=0.8/24,
              color_up="steelblue",
              color_down="tomato",
              alpha=0.9)

    # recompute axis limits to include all patches
    ax.relim()
    ax.autoscale_view()

    # optional: format x-axis ticks
    ax.xaxis.set_major_locator(mdates.HourLocator())
    ax.xaxis.set_major_formatter(mdates.DateFormatter("%H:%M"))

    ax.set_title("Grey = Daily Bar   Blue/Red = Hourly Bars")
    fig.autofmt_xdate()
    plt.tight_layout()

    from IBTool import IBTool
    import Global_Values
    from Logger import Logger
    from ibapi.contract import Contract

    Global_Values.initConfig()
    Global_Values.G_IsDebug = True
    Global_Values.G_Logger = Logger(Global_Values.G_Config, Global_Values.G_IsDebug)
    ib = IBTool(Global_Values.G_Config)
    ib.reconnect()

    contract = Contract()
    contract.symbol = "TSLA"
    contract.secType = "STK"
    contract.exchange = "SMART"
    contract.currency = "USD"
    contract.primaryExchange = "NASDAQ"

    req = ib.reqHistoricalData( reqId=1, contract=contract, endDateTime="20250729 20:00:00 US/Eastern", durationStr="1 y", barSizeSetting="5 mins", whatToShow="TRADES", useRTH=1, formatDate=1, keepUpToDate=False, chartOptions=[])
    req.done.delay_wait()

    path = Path("simulated_data/TSLA_5min.json")
    with path.open("w", encoding="utf-8") as f:
        json.dump(req.response.contents, f, indent=4)

    

    ib.disconnect()

    # block until window is closed
    plt.show(block=True)


    
