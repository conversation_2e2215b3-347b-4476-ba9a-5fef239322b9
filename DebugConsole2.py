# trade_console_pt.py
import sys, code, threading
from prompt_toolkit import PromptSession
from prompt_toolkit.completion import Completer, Completion
from TradeTool import TradeTool
import Global_Values

class TradeToolConsole:
    def __init__(self, tt):
        self.tt = tt
        self.session = PromptSession(completer=self._make_completer())

    def _make_completer(self):
        class C(Completer):
            def get_completions(inner, doc, _):
                text = doc.text_before_cursor
                if not text.startswith('.'):
                    return
                obj = self.tt
                parts = text[1:].split('.')
                for p in parts[:-1]:
                    obj = getattr(obj, p, None)
                    if obj is None:
                        return
                for attr in dir(obj):
                    if attr.startswith(parts[-1]):
                        yield Completion(attr, start_position=-len(parts[-1]))
        return C()

    def start(self):
        def loop():
            print("🔍 Debug console—type .ib.requests, CTRL-D to quit.")
            while True:
                try:
                    text = self.session.prompt(">>> ")
                except (EOFError, KeyboardInterrupt):
                    break
                if not text.startswith('.'):
                    print("↳ start with `.`")
                    continue
                try:
                    val = eval(f"tradeTool{text}")
                    print(repr(val))
                except Exception as e:
                    print(f"Error: {e!r}")
        threading.Thread(target=loop, daemon=True, name="TradeTool Debug Console").start()

if __name__ == '__main__':
    Global_Values.initConfig()
    tradeTool = TradeTool()
    TradeToolConsole(tradeTool).start()
    sys.exit(tradeTool.run())
