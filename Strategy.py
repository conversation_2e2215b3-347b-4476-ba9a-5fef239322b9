import time, hashlib, json
from threading import Thread ,RLock
from functools import wraps
from enum import IntEnum
from decimal import Decimal
from IBTool import IBTool
from ibapi.contract import Contract, ContractDetails
from ibapi.order import Order
from Comm import *
from SotckAnalysis import SotckAnalysis
from importlib import import_module
from pathlib import Path
import pytz
import Indicator
from ibapi.const import UNSET_DOUBLE
import Global_Values
from Logger import Logger
import copy
from DataConverter import DataConverter



class Strategy():
    class STRATEGY_TYPES(IntEnum):
        NONE = 0
        BUY_WHEN_BREAK_THROUGH = 1
        SELL = 2
        SELL_WHEN_FALL_AND_BUY_WHEN_REBOUND = 3


    class STRATEGY_EXECUTE_STATE(IntEnum):
        NONE = 0
        RUNNING = 1
        PAUSED = 2
        STOPPED = 3
    class Stack():
        class TYPE(IntEnum):
            GOTO = 0
            CALL = 1
            RET = 2
            FORWARD = 3

        def __init__(self, work: str, type:TYPE, forwordWorkWhenDone = None, forwordWorkWhenCancel = None, forwordWorkWhenUnvailable = None):
            self.work = work
            self.type = type
            self.forwordWorkWhenDone = forwordWorkWhenDone
            self.forwordWorkWhenCancel = forwordWorkWhenCancel
            self.forwordWorkWhenUnvailable = forwordWorkWhenUnvailable

        
        @staticmethod
        def restore(save: dict):
            return Strategy.Stack(
                save["work"],
                Strategy.Stack.TYPE(save["type"]),
                save.get("forwordWorkWhenDone"),
                save.get("forwordWorkWhenCancel"),
                save.get("forwordWorkWhenUnvailable")
            )
      

        def snapshot(self):
            return {
                "work": self.work,
                "type": self.type,
                "forwordWorkWhenDone": self.forwordWorkWhenDone,
                "forwordWorkWhenCancel": self.forwordWorkWhenCancel,
                "forwordWorkWhenUnvailable": self.forwordWorkWhenUnvailable
            }


    def __init__(self,ib: IBTool,contractDetail: ContractDetails, id: int, type: STRATEGY_TYPES =STRATEGY_TYPES.NONE):
   
        self.stacks: list[Strategy.Stack] = []
        self.state =Strategy.STRATEGY_EXECUTE_STATE.STOPPED
        self.lastExecTime = 0
        self.interval = 0
        self.goto = None
        self.call = None
        self.ret = False
        self.forward = None 
        self.gotoForwardTarget = None
        self.type = type
        self.ib = ib
        self.data : dict = {}
        self.data["filling_orders"] = []
        self.data["tradeHistory"] = []
        self.contractDetail = contractDetail
        self.id = -1
        self.Handel_OnDelete = None
        self.mgr :StrategyManager =  None
    
    def setStrategyManager(self, mgr):
        self.mgr = mgr
  
        
    @staticmethod
    def work(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            Global_Values.G_Logger.log(func.__qualname__,  f" Run : {self.contractDetail.contract.symbol} ", Logger.Level.GENERAL)    
            result = func(self, *args, **kwargs)
            
            self.lastExecTime = time.time()

            if self.goto is not None:
                self.stacks.pop()
                stack = Strategy.Stack(self.goto.__name__, Strategy.Stack.TYPE.GOTO)
                self.stacks.append(stack)
                self.goto = None
                self.interval = 0

            elif self.call is not None:
                stack = Strategy.Stack(self.call.__name__, Strategy.Stack.TYPE.CALL)
                self.stacks.append(stack)
                self.call = None
                self.interval = 0

            elif self.ret == True:
                self.stacks.pop()
                self.ret = False
                self.interval = 0

            elif self.forward is not None:
                self.stacks.pop()
                stack = Strategy.Stack(self.forward.__name__, Strategy.Stack.TYPE.FORWARD, self.forwordWorkWhenDone.__name__, self.forwordWorkWhenCancel.__name__, self.forwordWorkWhenUnvailable.__name__)
                self.stacks.append(stack)
                self.forward = None
                self.interval = 0

            elif self.gotoForwardTarget is not None:
                self.stacks.pop()
                stack = Strategy.Stack(self.gotoForwardTarget, Strategy.Stack.TYPE.FORWARD)
                self.stacks.append(stack)
                self.gotoForwardTarget = None
                self.interval = 0

            else:
                self.interval = inspect.signature(func).parameters['interval'].default

            
            return result
        return wrapper
    

    def start(self):
        if  self.state == Strategy.STRATEGY_EXECUTE_STATE.STOPPED:
            self.stacks.clear()
            stack = Strategy.Stack(self.begin.__name__, Strategy.Stack.TYPE.GOTO)
            self.stacks.append(stack)

            self.data["filling_orders"] = []

        self.state = Strategy.STRATEGY_EXECUTE_STATE.RUNNING
    
    def pause(self):
        self.state = Strategy.STRATEGY_EXECUTE_STATE.PAUSED

    def stop(self):
        self.on_remove()
        self.state = Strategy.STRATEGY_EXECUTE_STATE.STOPPED

    def get_next_work(self):
        if len(self.stacks) == 0:
            return None
        else:
            return self.stacks[-1].work

    @work
    def begin(self, interval = 0):
        pass
        
    @work
    def end(self, interval = 0):
    
        self.stacks.clear()
   

    def restore(self, save : dict):
        _basic = save["Basic"]


        self.stacks = [Strategy.Stack.restore(stack) for stack in _basic["stacks"]]
        self.state = Strategy.STRATEGY_EXECUTE_STATE(_basic["state"])
       
        self.lastExecTime = _basic["lastExecTime"]
        self.interval = _basic["interval"]
       
        self.contractDetail = DataConverter.dict_to_contractDetails(_basic["contractDetail"])
        self.data = save["Data"]
        self.data["contractDetail"] = self.contractDetail



    def snapshot(self):
        dataCopy = self.data.copy()
       
        contractDetail = DataConverter.contractDetails_to_dict(self.contractDetail)
        dataCopy["contractDetail"] = contractDetail

        return {
            "Basic": {
 
                "stacks": [stack.snapshot() for stack in self.stacks],
                "state": self.state,
                "lastExecTime": self.lastExecTime,
                "interval": self.interval,
                "contractDetail": contractDetail

            },
            "Data": dataCopy
        }   


    def _state_signature(self) -> bytes:
      
        payload = {
            "stacks"  : [stack.snapshot() for stack in self.stacks],
            "state"   : self.state
        }
        j = json.dumps(payload, sort_keys=True, separators=(",", ":")).encode()
        return hashlib.blake2b(j, digest_size=16).digest()  

    def mark_clean_signature(self) -> None:
        self._cached_sig = self._state_signature()

    def is_state_updated(self) -> bool:
       
        if not hasattr(self, "_cached_sig"):
            
            self.mark_clean_signature()
            return False
        return self._state_signature() != self._cached_sig



    @work
    def waiting_orders_fill(self, interval = 5):
        if self.stacks[-1].type != Strategy.Stack.TYPE.FORWARD:
            raise Exception("waiting_orders_fill: not called by forward")
        
        if self.data["filling_orders"] is None:
            raise Exception("waiting_orders_fill: filling_orders have not been set")


        if not self.ib.is_account_data_ready():
            Global_Values.G_Logger.log(f"Strategy_Buy_When_Break_Through.create_data_connection",  "Account data is not ready", Logger.Level.GENERAL)
            return



        hasOrderFilled = False
        hasOrderPending = False

        for order in self.data["filling_orders"]:
      
            orderId = order["orderId"]
            fillingType = order["Type"]
            isLogged =  order["isLogged"]

            _orderStatus =  self.ib.openStatusData.get(orderId)
            if _orderStatus is None:
                Global_Values.G_Logger.log(f"{self.__class__.__name__}.waiting_orders_fill",  f"Order {orderId} may be deleted directly from TWS, skipped it.", Logger.Level.GENERAL)
                continue

            now = datetime.now()
            dt = pytz.timezone(self.ib.config['TimeZone']).localize(now)
            formattedTime = dt.strftime("%Y-%m-%d %H:%M:%S") 
        

            if  _orderStatus["status"] == "Filled":

                    if order.get("stauts") is None:
                        trade={"type": fillingType, "Symbol": self.contractDetail.contract.symbol, "contract_type" : self.contractDetail.contract.secType, "Exchange": self.contractDetail.contract.exchange, "orderId": orderId, "filledTime": formattedTime, "avgFillPrice": _orderStatus["avgFillPrice"] , "filled": _orderStatus["filled"], "remaining": _orderStatus["remaining"]}
                        self.data["tradeHistory"].append(trade)
                        self.mgr.add_trade_history(trade)
                    
                        Global_Values.G_Logger.logTradeHistory(trade)
                        Global_Values.G_Logger.log(f"{self.__class__.__name__}.waiting_orders_fill",  f"Order Filled: {trade}", Logger.Level.GENERAL)

                        # order["isLogged"] = True
                        order["stauts"] = "Filled"
                    
                    hasOrderFilled = True

            elif _orderStatus["status"] == "Cancelled" or _orderStatus["status"] == "PendingCancel":
                
                    if order.get("stauts") is None:
                        Global_Values.G_Logger.log(f"{self.__class__.__name__}.waiting_orders_fill",  f"Order Cancelled: {orderId}", Logger.Level.GENERAL)
                        # order["isLogged"] = True
                        order["stauts"] = "Cancelled"

            else:
                    hasOrderPending = True
                    

        if not hasOrderPending:

            if  hasOrderFilled:
                if  self.stacks[-1].forwordWorkWhenDone is not None:
                    self.gotoForwardTarget = self.stacks[-1].forwordWorkWhenDone

            else:
                if  self.stacks[-1].forwordWorkWhenCancel is not None:
                    self.gotoForwardTarget = self.stacks[-1].forwordWorkWhenCancel


    def on_remove(self):
        pass
        
    

class Strategy_Buy_When_Break_Through(Strategy):
    def __init__(self, ib: IBTool, con: ContractDetails, id: int):
        super().__init__(ib, con, id, Strategy.STRATEGY_TYPES.BUY_WHEN_BREAK_THROUGH)
        
      
        self.reqDailyBars = None
        self.ib = ib
        self.SMA_VOL_LENGTH = 20

        self.stacks.append(Strategy.Stack(self.begin.__name__, Strategy.Stack.TYPE.GOTO))
       


    def setParms(self, parms: dict):
        self.data["break_through_price"] =  parms['break_through_price'] 
        self.data["huge_volume_rate"] =  parms['huge_volume_rate']
        self.data["check_num_of_bar_huge_volume"] =  parms['check_num_of_bar_huge_volume'] #if parms['check_num_of_bar_huge_volume'] > 0 else 10
        self.data["buy_share"] =  parms['buy_share']



    @Strategy.work
    def create_data_connection(self, interval = 5):
      

        if not self.ib.is_account_data_ready():
            Global_Values.G_Logger.log(f"Strategy_Buy_When_Break_Through.create_data_connection",  "Account data is not ready", Logger.Level.GENERAL)

            return

        if self.reqDailyBars is not None:
            self.ib.cancelHistoricalData(self.reqDailyBars.reqId)
  
        reqId  =  self.ib.get_next_reqID()
        self.reqDailyBars = self.ib.reqHistoricalData(reqId, self.contractDetail.contract , "", "1 Y", "1 day", "TRADES", 0, 1, True, [])
        self.reqDailyBars.done.delay_wait()

        if len(self.reqDailyBars.response.errors) == 0:
            lastStack = self.stacks[-1]
            if lastStack == Strategy.Stack.TYPE.CALL:
                self.ret = True
            else:
                self.goto = self.buy_when_break_through
                
    @Strategy.work
    def begin(self, interval = 0):
        super().begin.__wrapped__(self, interval)
    
        if self.reqDailyBars is None:
            self.goto = self.create_data_connection
        else: 
            self.goto = self.buy_when_break_through 



    #logic: 如果今天突破買入價位和今天或前幾天成交量比平時高出很多,買入
    @Strategy.work
    def buy_when_break_through(self, interval = 5):
       
        if self.reqDailyBars is None:
            self.call = self.create_data_connection
            return

        bars = self.ib.barsData[self.reqDailyBars.reqId]
        numOfBars = len(bars)
        if numOfBars < max(self.data["check_num_of_bar_huge_volume"], self.SMA_VOL_LENGTH):
            return

        sma_vol_20 = Indicator.sma(bars, field="volume", length=self.SMA_VOL_LENGTH)

        lastBar = self.ib.barsData[self.reqDailyBars.reqId].get_data(-1)
        if lastBar["close"] > self.data["break_through_price"]:
            
            is_huge_volume = False
            for i in range(numOfBars - 1, numOfBars -self.data["check_num_of_bar_huge_volume"]-1, -1):

                increaseRate = float(bars.get_data(i)["volume"]) / sma_vol_20[i]

                if (bars.get_data(i)["close"] > bars.get_data(i - 1)["open"]):
                    if increaseRate >= self.data["huge_volume_rate"]:
                        is_huge_volume = True
                        break


            if is_huge_volume:

                contract =   copy.copy(self.contractDetail.contract)
                contract.exchange = "SMART"
              
                order =  Order()
                order.tif = "GTC"
                order.outsideRth = True
                order.transmit = True
                order.action = "BUY"
                order.orderType = "LMT"
                price =  lastBar["close"] * 1.00001
                order.lmtPrice = OrderVaildator.roundPrice(price, self.contractDetail.minTick)
                order.totalQuantity =  OrderVaildator.roundQuantity(Decimal(self.data["buy_share"]), self.contractDetail.minSize, self.contractDetail.sizeIncrement)
                commission = self.ib.calculate_commission(contract, order)
                totalCost = order.lmtPrice * float(order.totalQuantity) + commission
                if self.ib.accountData[self.ib.accountName][('TotalCashBalance',contract.currency)]['val'] >= totalCost:

                    order.orderRef = f"{self.__class__.__name__}:{self.ib.nextOrderId}"

                    req = self.ib.placeOrder(contract, order)                  
                    req.done.delay_wait()
                    Global_Values.G_Logger.log(f"{self.__class__.__name__}.buy_when_break_through",  f"Place Order: contract: {contract} , order: {order} , reqId: {req.reqId}", Logger.Level.GENERAL)
                    
                    if len(req.response.errors) == 0:
                        self.data["filling_orders"].append({"Type": "BUY", "orderId": req.reqId, "isLogged": False})
                       
                        self.forward = self.waiting_orders_fill
                        self.forwordWorkWhenDone = self.end
                        self.forwordWorkWhenCancel = self.end
                        self.forwordWorkWhenUnvailable = self.end

                    else:
                        Global_Values.G_Logger.log(f"{self.__class__.__name__}.buy_when_break_through",  f"Place Order Error: {req.response.errors}", Logger.Level.GENERAL)
                        self.goto = self.end

    def on_remove(self):
        for order in self.data["filling_orders"]:
            if order["orderId"] in self.ib.openStatusData:
                req = self.ib.cancelOrder(order["orderId"])
                req.done.delay_wait()

        if self.reqDailyBars is not None:
            req = self.ib.cancelHistoricalData(self.reqDailyBars.reqId)
            req.done.delay_wait()



        

class Strategy_Fall_Sell_Rebound_Buy(Strategy):
    def __init__(self, ib: IBTool, con: ContractDetails, id: int, parms: dict ={}):
        super().__init__(ib,con, id, Strategy.STRATEGY_TYPES.SELL_WHEN_FALL_AND_BUY_WHEN_REBOUND)
        self.data["contract"] = parms['contract']
        self.data["filling_orders"] = []
        self.data["TradeHistory"] = []
        self.data["start_price"] =  parms['start_price'] if parms['start_price'] is not None else 0 
        self.reqDailyBars = None
        self.reqFiveMinsBars = None
        self.req30SecBars = None
        

    @Strategy.work
    def create_data_connection(self, interval = 5):
     

        if not self.ib.is_account_data_ready():
            Global_Values.G_Logger.log(f"Strategy_Fall_Sell_Rebound_Buy.create_data_connection",  "Account data is not ready", Logger.Level.DETAIL)
            return

        if self.reqDailyBars is not None:
            self.ib.cancelHistoricalData(self.reqDailyBars.reqId)

        contract =  self.contract
        reqId =  self.ib.get_next_reqID()
        self.reqDailyBars =self.ib.reqHistoricalData(reqId, contract, "", "5 Y", "1 Days", "TRADES", 0, 1, True, [])
        self.reqDailyBars.done.delay_wait()

        reqId =  self.ib.get_next_reqID()
        self.reqFiveMinsBars =self.ib.reqHistoricalData(reqId, contract, "", "1 D", "5 mins", "TRADES", 0, 1, True, [])
        self.reqFiveMinsBars.done.delay_wait()

        reqId =  self.ib.get_next_reqID()
        self.req30SecBars =self.ib.reqHistoricalData(reqId, contract, "", "30 S", "1 sec", "TRADES", 0, 1, True, [])
        self.req30SecBars.done.delay_wait()

  
        if len(self.reqDailyBars.response.errors) == 0:
           
           if self._retWork is not None :
                self._nextWork = self._retWork
                return
           
           self.goto = self.sell_when_fall
    

    @Strategy.work
    def sell_when_fall(self, interval = 5):
       
     
        if self.reqDailyBars is None or self.reqFiveMinsBars is None or self.req30SecBars is None:
            self.call = self.create_data_connection
            return

        lastBar = self.ib.barsData[self.req30SecBars.reqId][-1]
        
        if self.data["start_price"] is None:
            self.data["start_price"] = lastBar.close

        else:
            if  lastBar.close /self.data["start_price"]  <= 0.9:
                contract =  self.contract
                portfolio = self.ib.findPortfolio(contract)
                if portfolio is not None:
                    #IB is not allowed 碎股 in api
                    if portfolio['position'] >= 1:
                        order =  Order()
                        order.action = "SELL"
                        order.orderType = "LMT"
                        share_sell = round(portfolio['position'])
                        order.totalQuantity =  Decimal(share_sell)
                        order.lmtPrice =  ((lastBar.low + lastBar.high) / 2) * 0.999

                        commission = self.ib.calculate_commission(contract, order)
                     
                        if self.ib.accountData[self.ib.accountName]['NetLiquidation']['val'] > commission:
                            req = self.ib.placeOrder(contract, order)
                            req.done.delay_wait()

                        if len(req.response.errors) == 0:
                            self.data["filling_orders"] = {"Type": "SELL", "orderId": req.reqId}
                            self.goto = self.waiting_orders_fill

             
       
    
    @Strategy.work
    def waiting_orders_fill(self, interval = 5):
       

        if self.data["filling_orders"] is None:
            raise Exception("waiting_orders_fill: filling_orders have not been set")


        orderId = self.data["filling_orders"]["orderId"]
        fillingType = self.data["filling_orders"]["Type"]

    
        _orderStatus =  self.ib.openStatusData[orderId]
    
        now = datetime.now()
        dt = pytz.timezone(self.ib.config['TimeZone']).localize(now)
        formattedTime = dt.strftime("%Y-%m-%d %H:%M:%S") 
    

        if  _orderStatus["status"] == "Filled":
            if fillingType == "SELL":
                self.data["TradeHistory"].append({"type":"SELL","orderId": orderId, "filledTime": formattedTime, "avgFillPrice": _orderStatus["avgFillPrice"] , "filled": _orderStatus["filled"], "remaining": _orderStatus["remaining"]})
                self.goto = self.buy_when_rebound
            else:
                self.data["TradeHistory"].append({"type":"BUY","orderId": orderId, "filledTime": formattedTime, "avgFillPrice": _orderStatus["avgFillPrice"] , "filled": _orderStatus["filled"],"remaining": _orderStatus["remaining"]})
                self.goto = self.sell_when_fall

        elif _orderStatus["status"] == "Cancelled" or _orderStatus["status"] == "PendingCancel":
                if fillingType == "SELL":
                    self.goto = self.sell_when_fall
                else:
                    self.goto = self.buy_when_rebound
              
              

    @Strategy.work
    def buy_when_rebound(self, interval = 5):
    

        if self.reqFiveMinsBars is None or self.req30SecBars is None:
            self.call = self.create_data_connection
            return

        stockAnalysis = SotckAnalysis(self.ib.barsData[self.reqFiveMinsBars.reqId])

        if not stockAnalysis.is_heavy_sell() and stockAnalysis.is_weight_of_sell_decrease() and stockAnalysis.is_heavy_buy():
            lastSell = None
            for i in  range(len(self.data["TradeHistory"]) - 1, -1, -1):
                if self.data["TradeHistory"][i]["type"] == "SELL":
                    lastSell = self.data["TradeHistory"][i]
                    break

            lastBar = self.ib.barsData[self.req30SecBars.reqId][-1]
            order =  Order()
            order.action = "BUY"
            order.orderType = "LMT"
            order.lmtPrice =  ((lastBar.low + lastBar.high) / 2) * 1.001

            
            
            if lastSell is not None:
                lastTotalPrice = lastSell["filledPrice"] * lastSell["filledQuantity"]
                share_buy = round(lastTotalPrice / order.lmtPrice)
                share_buy = max(share_buy, 1)
                order.totalQuantity =  Decimal(share_buy)
               

            commission = self.ib.calculate_commission(self.contract, order)
            totalCost = order.lmtPrice * order.totalQuantity + commission
            
            if self.ib.accountData[self.ib.accountName]['NetLiquidation']['val'] >= totalCost:
                req = self.ib.placeOrder(self.contract, order)
                req.done.delay_wait()

                if len(req.response.errors) == 0:
                    self.data["filling_orders"] = {"Type": "BUY", "orderId": req.reqId}
                    self.goto = self.waiting_orders_fill
                    
STRATEGY_REGISTRY = {
    "None":   Strategy,
    "Strategy_Fall_Sell_Rebound_Buy": Strategy_Fall_Sell_Rebound_Buy,
    "Strategy_Buy_When_Break_Through": Strategy_Buy_When_Break_Through
}


class StrategyManager():
    def __init__(self, config: dict, ib: IBTool):
        self.strategies : list[Strategy] = []
        self.thread = None
        self.lock = RLock()
        self.config =  config
        self.ib = ib
        self.pendingUpdateStrategies: list[dict] = []
        self.pendingDeleteStrategies : list[Strategy] = []
        self.pendingAddStrategies : list[Strategy] = []
        self.timer = Event()
        self.Handle_onChnage = None
        self.running = False

        self.tradeHistory: list[dict] = []
    

    def setOnChnage(self, handle):
        self.Handle_onChnage = handle

    def loop(self):
        self.running = True
        while True:
            
            for strategy in self.pendingAddStrategies:
                self.strategies.append(strategy)
                strategy.mark_clean_signature()
                strategy.start()

                if self.Handle_onChnage is not None:
                    self.Handle_onChnage(self, "add", strategy)
            
            for item in self.pendingUpdateStrategies:
                strategy = item["strategy"]
                parms = item["parms"]
                strategy.setParms(parms)

                if self.Handle_onChnage is not None:
                    self.Handle_onChnage(self,"edit", strategy )
            
            for strategy in self.pendingDeleteStrategies:
                strategy.on_remove()
                self.strategies.remove(strategy)
                
                if self.Handle_onChnage is not None:
                    self.Handle_onChnage(self, "delete", strategy)
            
            
            if  self.hasInput():
                self.pendingAddStrategies.clear()
                self.pendingUpdateStrategies.clear()
                self.pendingDeleteStrategies.clear()
              
                self.save()

                

            
            if not self.running:
                break

            with self.lock:
                ref_strategies = list(self.strategies) 
            
            for strategy in ref_strategies:
                if self.hasInput():
                    break

                if strategy.state != Strategy.STRATEGY_EXECUTE_STATE.RUNNING:
                    continue
                
                if strategy.lastExecTime + strategy.interval > time.time():
                    continue
                

                _nextWorkName = strategy.get_next_work()
                if _nextWorkName is not None :
                  
                    nextWork = getattr(strategy, _nextWorkName, None)
                    if callable(nextWork):
                        nextWork()
                        if strategy.is_state_updated():
                            self.save()
                            strategy.mark_clean_signature() 

                                              
                else:
                    self.tradeHistory.append(strategy.data["tradeHistory"])
                    self.pendingDeleteStrategies.append(strategy)
                    # Global_Values.G_Logger.log(f"StrategyManager",  f"Strategy {strategy.__class__.__name__} Ended", Logger.Level.GENERAL)

                


            closestNextSleep = 10
            with self.lock:
                now = time.time()
                for st in self.strategies:
                  
                    if st.state == Strategy.STRATEGY_EXECUTE_STATE.RUNNING:
                        timeDif= (st.lastExecTime + st.interval) - now
                        timeDif = max(0.0, timeDif)
                        closestNextSleep = min(closestNextSleep, timeDif)

            self.timer.wait(closestNextSleep)
            self.timer.clear()


    def hasInput(self):
        return len(self.pendingAddStrategies) > 0 or len(self.pendingUpdateStrategies) > 0 or len(self.pendingDeleteStrategies) > 0


    def start(self):
        self.thread = Thread(target=self.loop, name="Strategy Manager", daemon=True)
        self.thread.start()

    def stop(self):
        self.running = False
        self.timer.set() 

    def load(self) -> None:
        path: Path = Path(self.config["StrategySaveFile"])

        if not path.exists():
            Global_Values.G_Logger.log("StrategyManager", f"can't find StragyList file {path}", Logger.Level.GENERAL)
            return


        with path.open("r", encoding="utf-8") as f:
                records = json.load(f)              
      

        self.strategies.clear()

        for rec in records:
            cls_name: str  = rec["name"]            
            snapshot: dict = rec["snapshot"]
            _id: int = rec["idx"]
            contactDetail:ContractDetails = snapshot["Basic"]["contractDetail"]

            cls = STRATEGY_REGISTRY[cls_name]
         
            strategy = cls(ib=self.ib, con=contactDetail, id=_id)
            strategy.setStrategyManager(self)                 
            strategy.restore(snapshot)                 
            strategy.mark_clean_signature()            

            self.strategies.append(strategy)



    def save(self) -> None:
        dump = [{
                        "idx":  i  , 
                        "name": s.__class__.__name__,
                        "snapshot": s.snapshot()
                } for i, s in enumerate(self.strategies)]
    
        with open(self.config["StrategySaveFile"],"w", encoding="utf-8") as f:
            json.dump(dump, f, ensure_ascii=False, indent=2)    




    def get_next_id(self):
        with self.lock:
            ref_strategies = list(self.strategies) 

        max_id = 0
        for s in ref_strategies:
            max_id = max(max_id, s.id)
            
        max_id += 1

        return max_id

    def add(self, strategy):
        strategy.id = self.get_next_id()
        strategy.setStrategyManager(self)
        self.pendingAddStrategies.append(strategy)
       
        self.timer.set()
    
    
    def remove(self, strategy):
        strategy.stop()
        self.pendingDeleteStrategies.append(strategy)
       
        self.timer.set()

    
    def update(self, strategy, new_parms):
        item =  {"strategy": strategy, "parms": new_parms}
        self.pendingUpdateStrategies.append(item)
       
        self.timer.set()

    def get_strategy_by_contract(self,  contractDetail: ContractDetails):
        with self.lock:
            ref_strategies = list(self.strategies) 
        
        foundStrategy = []
        for strategy in ref_strategies:
            if strategy.contractDetail.contract.symbol == contractDetail.contract.symbol and strategy.contractDetail.contract.secType == contractDetail.contract.secType and strategy.contractDetail.contract.exchange == contractDetail.contract.exchange and strategy.contractDetail.contract.currency == contractDetail.contract.currency:
                foundStrategy.append(strategy)
        return foundStrategy
    
    def  add_trade_history(self, tradeHistory):
        self.tradeHistory.append(tradeHistory)
        


    