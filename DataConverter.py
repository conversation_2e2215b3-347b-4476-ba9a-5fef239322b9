from decimal import Decimal
from ibapi.const import UNSET_INTEGER, UNSET_DOUBLE, UNSET_DECIMAL
from ibapi.contract import ContractDetails, Contract, ComboLeg, DeltaNeutralContract, FundAssetType, FundDistributionPolicyIndicator
from ibapi.utils import intMaxString, floatMaxString, decimalMaxString
from enum import Enum
from ibapi.common import  BarData
from ibapi.tag_value import TagValue 
from datetime import datetime
import pytz as ZoneInfo

# # Enum definitions as provided
# class FundDistributionPolicyIndicator(Enum):
#     NoneItem = ("None", "None")
#     AccumulationFund = ("N", "Accumulation Fund")
#     IncomeFund = ("Y", "Income Fund")

# class FundAssetType(Enum):
#     NoneItem = ("None", "None")
#     Others = ("000", "Others")
#     MoneyMarket = ("001", "Money Market")
#     FixedIncome = ("002", "Fixed Income")
#     MultiAsset = ("003", "Multi-asset")
#     Equity = ("004", "Equity")
#     Sector = ("005", "Sector")
#     Guaranteed = ("006", "Guaranteed")
#     Alternative = ("007", "Alternative")


class DataConverter():
    # Enum value mappings for efficient lookup
    fund_distribution_policy_map = {member.value[0]: member for member in FundDistributionPolicyIndicator}
    fund_asset_type_map = {member.value[0]: member for member in FundAssetType}

    @staticmethod
    def dict_to_combo_leg(data: dict) -> ComboLeg:
        leg = ComboLeg()
        leg.conId = UNSET_INTEGER if data.get('conId', '') == '' else int(data['conId'])
        leg.ratio = UNSET_INTEGER if data.get('ratio', '') == '' else int(data['ratio'])
        leg.action = data.get('action', '')
        leg.exchange = data.get('exchange', '')
        leg.openClose = UNSET_INTEGER if data.get('openClose', '') == '' else int(data['openClose'])
        leg.shortSaleSlot = UNSET_INTEGER if data.get('shortSaleSlot', '') == '' else int(data['shortSaleSlot'])
        leg.designatedLocation = data.get('designatedLocation', '')
        leg.exemptCode = UNSET_INTEGER if data.get('exemptCode', '') == '' else int(data['exemptCode'])
        return leg

    @staticmethod
    def dict_to_delta_neutral_contract(data: dict) -> DeltaNeutralContract:
        dnc = DeltaNeutralContract()
        dnc.conId = UNSET_INTEGER if data.get('conId', '') == '' else int(data['conId'])
        dnc.delta = UNSET_DOUBLE if data.get('delta', '') == '' else float(data['delta'])
        dnc.price = UNSET_DOUBLE if data.get('price', '') == '' else float(data['price'])
        return dnc

    @staticmethod
    def dict_to_contract(data: dict) -> Contract:
        contract = Contract()
        contract.conId = UNSET_INTEGER if data.get('conId', '') == '' else int(data['conId'])
        contract.symbol = data.get('symbol', '')
        contract.secType = data.get('secType', '')
        contract.lastTradeDateOrContractMonth = data.get('lastTradeDateOrContractMonth', '')
        contract.lastTradeDate = data.get('lastTradeDate', '')
        contract.strike = UNSET_DOUBLE if data.get('strike', '') == '' else float(data['strike'])
        contract.right = data.get('right', '')
        contract.multiplier = data.get('multiplier', '')
        contract.exchange = data.get('exchange', '')
        contract.primaryExchange = data.get('primaryExchange', '')
        contract.currency = data.get('currency', '')
        contract.localSymbol = data.get('localSymbol', '')
        contract.tradingClass = data.get('tradingClass', '')
        contract.includeExpired = data.get('includeExpired', 'false').lower() == 'true'
        contract.secIdType = data.get('secIdType', '')
        contract.secId = data.get('secId', '')
        contract.description = data.get('description', '')
        contract.issuerId = data.get('issuerId', '')
        contract.comboLegsDescrip = data.get('comboLegsDescrip', '')
        contract.comboLegs = [DataConverter.dict_to_combo_leg(leg) for leg in data.get('comboLegs', [])]
        if 'deltaNeutralContract' in data and data['deltaNeutralContract'] is not None:
            contract.deltaNeutralContract = DataConverter.dict_to_delta_neutral_contract(data['deltaNeutralContract'])
        return contract

    @staticmethod
    def dict_to_contractDetails(data: dict) -> ContractDetails:
        cd = ContractDetails()
        cd.contract = DataConverter.dict_to_contract(data.get('contract', {}))
        cd.marketName = data.get('marketName', '')
        cd.minTick = UNSET_DOUBLE if data.get('minTick', '') == '' else float(data['minTick'])
        cd.orderTypes = data.get('orderTypes', '')
        cd.validExchanges = data.get('validExchanges', '')
        cd.priceMagnifier = UNSET_INTEGER if data.get('priceMagnifier', '') == '' else int(data['priceMagnifier'])
        cd.underConId = UNSET_INTEGER if data.get('underConId', '') == '' else int(data['underConId'])
        cd.longName = data.get('longName', '')
        cd.contractMonth = data.get('contractMonth', '')
        cd.industry = data.get('industry', '')
        cd.category = data.get('category', '')
        cd.subcategory = data.get('subcategory', '')
        cd.timeZoneId = data.get('timeZoneId', '')
        cd.tradingHours = data.get('tradingHours', '')
        cd.liquidHours = data.get('liquidHours', '')
        cd.evRule = data.get('evRule', '')
        cd.evMultiplier = UNSET_INTEGER if data.get('evMultiplier', '') == '' else int(data['evMultiplier'])
        cd.aggGroup = UNSET_INTEGER if data.get('aggGroup', '') == '' else int(data['aggGroup'])
        cd.underSymbol = data.get('underSymbol', '')
        cd.underSecType = data.get('underSecType', '')
        cd.marketRuleIds = data.get('marketRuleIds', '')
        if data.get('secIdList') is not None:
            cd.secIdList = [TagValue(item['tag'], item['value']) for item in data['secIdList']]
        else:
            cd.secIdList = None
        cd.realExpirationDate = data.get('realExpirationDate', '')
        cd.lastTradeTime = data.get('lastTradeTime', '')
        cd.stockType = data.get('stockType', '')
        
        # Handle Decimal fields with unset value checks
        min_size_str = data.get('minSize', '')
        if min_size_str in ('', '2147483647', '9223372036854775807', '1.7976931348623157E308', '-9223372036854775808'):
            cd.minSize = UNSET_DECIMAL
        else:
            cd.minSize = Decimal(min_size_str)
        
        size_increment_str = data.get('sizeIncrement', '')
        if size_increment_str in ('', '2147483647', '9223372036854775807', '1.7976931348623157E308', '-9223372036854775808'):
            cd.sizeIncrement = UNSET_DECIMAL
        else:
            cd.sizeIncrement = Decimal(size_increment_str)
        
        suggested_size_increment_str = data.get('suggestedSizeIncrement', '')
        if suggested_size_increment_str in ('', '2147483647', '9223372036854775807', '1.7976931348623157E308', '-9223372036854775808'):
            cd.suggestedSizeIncrement = UNSET_DECIMAL
        else:
            cd.suggestedSizeIncrement = Decimal(suggested_size_increment_str)
        
        cd.cusip = data.get('cusip', '')
        cd.ratings = data.get('ratings', '')
        cd.descAppend = data.get('descAppend', '')
        cd.bondType = data.get('bondType', '')
        cd.couponType = data.get('couponType', '')
        cd.callable = data.get('callable', 'false').lower() == 'true'
        cd.putable = data.get('putable', 'false').lower() == 'true'
        cd.coupon = UNSET_DOUBLE if data.get('coupon', '') == '' else float(data['coupon'])
        cd.convertible = data.get('convertible', 'false').lower() == 'true'
        cd.maturity = data.get('maturity', '')
        cd.issueDate = data.get('issueDate', '')
        cd.nextOptionDate = data.get('nextOptionDate', '')
        cd.nextOptionType = data.get('nextOptionType', '')
        cd.nextOptionPartial = data.get('nextOptionPartial', 'false').lower() == 'true'
        cd.notes = data.get('notes', '')
        cd.fundName = data.get('fundName', '')
        cd.fundFamily = data.get('fundFamily', '')
        cd.fundType = data.get('fundType', '')
        cd.fundFrontLoad = data.get('fundFrontLoad', '')
        cd.fundBackLoad = data.get('fundBackLoad', '')
        cd.fundBackLoadTimeInterval = data.get('fundBackLoadTimeInterval', '')
        cd.fundManagementFee = data.get('fundManagementFee', '')
        cd.fundClosed = data.get('fundClosed', 'false').lower() == 'true'
        cd.fundClosedForNewInvestors = data.get('fundClosedForNewInvestors', 'false').lower() == 'true'
        cd.fundClosedForNewMoney = data.get('fundClosedForNewMoney', 'false').lower() == 'true'
        cd.fundNotifyAmount = data.get('fundNotifyAmount', '')
        cd.fundMinimumInitialPurchase = data.get('fundMinimumInitialPurchase', '')
        cd.fundSubsequentMinimumPurchase = data.get('fundSubsequentMinimumPurchase', '')
        cd.fundBlueSkyStates = data.get('fundBlueSkyStates', '')
        cd.fundBlueSkyTerritories = data.get('fundBlueSkyTerritories', '')
        indicator_val = data.get('fundDistributionPolicyIndicator', '')
        cd.fundDistributionPolicyIndicator = DataConverter.fund_distribution_policy_map.get(indicator_val, FundDistributionPolicyIndicator.NoneItem)
        asset_type_val = data.get('fundAssetType', '')
        cd.fundAssetType = DataConverter.fund_asset_type_map.get(asset_type_val, FundAssetType.NoneItem)
        cd.ineligibilityReasonList = data.get('ineligibilityReasonList', None)
        return cd

    @staticmethod
    def combo_leg_to_dict(leg: ComboLeg) -> dict:
        return {
            'conId': intMaxString(leg.conId),
            'ratio': intMaxString(leg.ratio),
            'action': leg.action,
            'exchange': leg.exchange,
            'openClose': intMaxString(leg.openClose),
            'shortSaleSlot': intMaxString(leg.shortSaleSlot),
            'designatedLocation': leg.designatedLocation,
            'exemptCode': intMaxString(leg.exemptCode),
        }

    @staticmethod
    def delta_neutral_contract_to_dict(dnc: DeltaNeutralContract) -> dict:
        return {
            'conId': intMaxString(dnc.conId),
            'delta': floatMaxString(dnc.delta),
            'price': floatMaxString(dnc.price),
        }

    @staticmethod
    def contract_to_dict(contract: Contract) -> dict:
        d = {
            'conId': intMaxString(contract.conId),
            'symbol': contract.symbol,
            'secType': contract.secType,
            'lastTradeDateOrContractMonth': contract.lastTradeDateOrContractMonth,
            'lastTradeDate': contract.lastTradeDate,
            'strike': floatMaxString(contract.strike),
            'right': contract.right,
            'multiplier': contract.multiplier,
            'exchange': contract.exchange,
            'primaryExchange': contract.primaryExchange,
            'currency': contract.currency,
            'localSymbol': contract.localSymbol,
            'tradingClass': contract.tradingClass,
            'includeExpired': str(contract.includeExpired).lower(),
            'secIdType': contract.secIdType,
            'secId': contract.secId,
            'description': contract.description,
            'issuerId': contract.issuerId,
            'comboLegsDescrip': contract.comboLegsDescrip,
            'comboLegs': [DataConverter.combo_leg_to_dict(leg) for leg in contract.comboLegs],
        }
        if contract.deltaNeutralContract:
            d['deltaNeutralContract'] = DataConverter.delta_neutral_contract_to_dict(contract.deltaNeutralContract)
        else:
            d['deltaNeutralContract'] = None
        return d

    @staticmethod
    def contractDetails_to_dict(cd: ContractDetails) -> dict:
        d = {
            'contract': DataConverter.contract_to_dict(cd.contract),
            'marketName': cd.marketName,
            'minTick': floatMaxString(cd.minTick),
            'orderTypes': cd.orderTypes,
            'validExchanges': cd.validExchanges,
            'priceMagnifier': intMaxString(cd.priceMagnifier),
            'underConId': intMaxString(cd.underConId),
            'longName': cd.longName,
            'contractMonth': cd.contractMonth,
            'industry': cd.industry,
            'category': cd.category,
            'subcategory': cd.subcategory,
            'timeZoneId': cd.timeZoneId,
            'tradingHours': cd.tradingHours,
            'liquidHours': cd.liquidHours,
            'evRule': cd.evRule,
            'evMultiplier': intMaxString(cd.evMultiplier),
            'aggGroup': intMaxString(cd.aggGroup),
            'underSymbol': cd.underSymbol,
            'underSecType': cd.underSecType,
            'marketRuleIds': cd.marketRuleIds,
            'secIdList': [{'tag': tv.tag, 'value': tv.value} for tv in cd.secIdList] if cd.secIdList else None,
            'realExpirationDate': cd.realExpirationDate,
            'lastTradeTime': cd.lastTradeTime,
            'stockType': cd.stockType,
            'minSize': decimalMaxString(cd.minSize),
            'sizeIncrement': decimalMaxString(cd.sizeIncrement),
            'suggestedSizeIncrement': decimalMaxString(cd.suggestedSizeIncrement),
            'cusip': cd.cusip,
            'ratings': cd.ratings,
            'descAppend': cd.descAppend,
            'bondType': cd.bondType,
            'couponType': cd.couponType,
            'callable': str(cd.callable).lower(),
            'putable': str(cd.putable).lower(),
            'coupon': floatMaxString(cd.coupon),
            'convertible': str(cd.convertible).lower(),
            'maturity': cd.maturity,
            'issueDate': cd.issueDate,
            'nextOptionDate': cd.nextOptionDate,
            'nextOptionType': cd.nextOptionType,
            'nextOptionPartial': str(cd.nextOptionPartial).lower(),
            'notes': cd.notes,
            'fundName': cd.fundName,
            'fundFamily': cd.fundFamily,
            'fundType': cd.fundType,
            'fundFrontLoad': cd.fundFrontLoad,
            'fundBackLoad': cd.fundBackLoad,
            'fundBackLoadTimeInterval': cd.fundBackLoadTimeInterval,
            'fundManagementFee': cd.fundManagementFee,
            'fundClosed': str(cd.fundClosed).lower(),
            'fundClosedForNewInvestors': str(cd.fundClosedForNewInvestors).lower(),
            'fundClosedForNewMoney': str(cd.fundClosedForNewMoney).lower(),
            'fundNotifyAmount': cd.fundNotifyAmount,
            'fundMinimumInitialPurchase': cd.fundMinimumInitialPurchase,
            'fundSubsequentMinimumPurchase': cd.fundSubsequentMinimumPurchase,
            'fundBlueSkyStates': cd.fundBlueSkyStates,
            'fundBlueSkyTerritories': cd.fundBlueSkyTerritories,
            'fundDistributionPolicyIndicator': cd.fundDistributionPolicyIndicator.value[0] if cd.fundDistributionPolicyIndicator else None,
            'fundAssetType': cd.fundAssetType.value[0] if cd.fundAssetType else None,
            'ineligibilityReasonList': cd.ineligibilityReasonList,
        }
        return d


    @staticmethod
    def convert_bardata_to_dict( bar: BarData):
        return {
            "date": DataConverter.convert_time_to_utc(bar.date) if len(bar.date) >8 else f"{bar.date[0:4]}-{bar.date[4:6]}-{bar.date[6:8]}",  #f"{bar.date[0:4]}-{bar.date[4:6]}-{bar.date[6:8]} {bar.date[9:17]}",
            "open": bar.open,
            "high": bar.high,
            "low": bar.low,
            "close": bar.close,
            "volume": int(bar.volume),
            "wap": bar.wap,
            "barCount": bar.barCount
        }
    

    @staticmethod
    def convert_time_to_utc(s: str):
        """
        將 "YYYYMMDD HH:MM:SS TZ" 轉成 UTC timestamp (整數秒)
        範例輸入："20250623 13:15:00 US/Eastern"
        """
        # 切出 datetime 部分和時區字符串
        dt_part, tz_name = s.rsplit(" ", 1)
        # 先做 naive datetime
        dt_naive = datetime.strptime(dt_part, "%Y%m%d %H:%M:%S")
        # 建立時區物件

        tz = ZoneInfo.timezone(tz_name)       # pytz 模組

        # 指定時區
        dt_loc = dt_naive.replace(tzinfo=tz)
        # 回傳整數秒
        return int(dt_loc.timestamp())

