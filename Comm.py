from threading import Thread, Event, current_thread
import time
from datetime import datetime
from enum import IntEnum
import debugpy 
import inspect
from ibapi.contract import Contract
from ibapi.const import UNSET_DOUBLE
from ibapi.common import  BarData
import pytz as ZoneInfo
from decimal import Decimal, ROUND_HALF_UP
import json

class WARNING_CANCEL_UPDATEACCOUNT:
    MESSAGES = {
        2100: "从 TWS请 求 了 新 账 户 数 据 。 API客 户 已 从 账 户 数 据 中 取 消 订阅 。"
    }


class WARNING:
    MESSAGES = {
        399,  "Order Message: BUY 1 TSLA NASDAQ.NMS Warning: Your order will not be placed at the exchange until xx time.",
        10167, "Requested market data is not subscribed. Displaying delayed market data. "
    }    

class INFO_TWS_AVILABLE:
    FAIL_MESSAGES = {
        1100: "Connectivity between IBKR and Trader Workstation has been lost., contract",
    }

    SUCCESS_MESSAGES = {
        1102:"Connectivity between IBKR and Trader Workstation has been restored - data maintained. All data farms are connected: usfarm.nj; jfarm; cafarm; cashfarm; usfarm; euhmds; fundfarm; ushmds; secdefil., contract"
    }

class ERROR_CONNECTION():
    MESSAGES = {
        502:"不 能 连 接 TWS。 通 过 Configure>API( 配 置 >API) 菜 单 指 令 确认 API已 在 TWS中 启 用",
        503:"你 的 TWS版 本 已 过 期 , 必 须 更 新",
        504:"没 有 连 接 。",
        1100: "Connectivity between IBKR and Trader Workstation has been lost., contract"
    }

class MESSAGE_USELESS:
    MESSAGES = [
        "farm connection is OK"
    ]


class CLIENT_REQUEST_ERRORS:
    CONNECTION_UNAVAILABLE = {"errorCode":99910,"errorString":"Connection unavailable"}
    TIME_OUT = {"errorCode":99911,"errorString":"Request timed out"}


class MESSAGE_DONE:
    MESSAGES = {
        202: "Order Canceled",
        162: "API historical data query cancelled"
    }

class MESSAGE_INPUT_ERROR:
    MESSAGES = {
        321: "Input Incorrect",
        366: "No historical data query found for ticker id:0 contract:"
    }

class TickerID_RULES:
    SYSTEM = -1
    REQUEST_ID_MIN = 0
    REQUEST_ID_MAX = 100
    


class REQUEST_TYPES(IntEnum):
    PLACE_ORDER = 1
    UPDATE_ORDER = 2
    CANCEL_ORDER = 3
    GET_OPEN_ORDERS = 4
    GET_EXECUTIONS = 5
    GET_COMPLETED_ORDERS = 6
    GET_POSITIONS = 7
    CANCEL_POSITIONS = 8
    GET_IDS = 9
    UPDATE_ACCOUNT_SUMMARY = 10
    CANCEL_ACCOUNT_SUMMARY = 11
    UPDATE_ACCOUNT = 12
    CANCEL_UPDATE_ACCOUNT = 13
    GET_HISTORICAL_DATA = 14
    CANCEL_HISTORICAL_DATA = 15
    GET_REAL_TIME_BARS = 16       
    CANCEL_REAL_TIME_BARS = 17     
    GET_MARKET_DATA = 18
    CANCEL_MARKET_DATA = 19
    GET_HISTORICAL_TICKS = 20
    CANCEL_HISTORICAL_TICKS = 21
    GET_TICK_BY_TICK_DATA = 22
    CANCEL_TICK_BY_TICK_DATA = 23
    GET_FUNDAMENTAL_DATA = 24
    CANCEL_FUNDAMENTAL_DATA = 25
    GET_MARKET_DEPTH = 26
    CANCEL_MARKET_DEPTH = 27
    GET_MARKET_DEPTH_L2 = 28
    CANCEL_MARKET_DEPTH_L2 = 29
    GET_CURRENT_TIME = 30
    GET_MARKET_DATA_TYPE = 31
    CONNECT = 32
    GET_CONTRACT_DETAILS = 33
    GET_WSH_EVENT_DATA = 34
    CANCEL_WSH_EVENT_DATA = 35
    GET_WSH_META_DATA = 36
    CANCEL_WSH_META_DATA = 37
    GET_HEAD_TIME_STAMP = 38
    CANCEL_HEAD_TIME_STAMP = 39
    REQUEST_ERROR = 99999

class REQUEST_TYPES_ALLOW_DUPLICATE:
    LIST = [REQUEST_TYPES.GET_HISTORICAL_DATA,REQUEST_TYPES.GET_MARKET_DATA, REQUEST_TYPES.CANCEL_MARKET_DATA]
   

class REQUEST_DATA_NUM_ALIVE_LIMIT:
    TYPE_LIST = [REQUEST_TYPES.GET_HISTORICAL_DATA , REQUEST_TYPES.GET_REAL_TIME_BARS]
    MAX =50

class REQUEST_DATA_NUM_TEN_MINS_LIMIT:
    TYPE_LIST = [REQUEST_TYPES.GET_HISTORICAL_DATA, REQUEST_TYPES.GET_REAL_TIME_BARS]
    MAX = 60


COUPLE_SUBSCRIBE_REQUESTS = {
    REQUEST_TYPES.CANCEL_UPDATE_ACCOUNT: REQUEST_TYPES.UPDATE_ACCOUNT,
    REQUEST_TYPES.CANCEL_ACCOUNT_SUMMARY: REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY,
    REQUEST_TYPES.CANCEL_HISTORICAL_DATA: REQUEST_TYPES.GET_HISTORICAL_DATA,
    REQUEST_TYPES.CANCEL_REAL_TIME_BARS: REQUEST_TYPES.GET_REAL_TIME_BARS,
    REQUEST_TYPES.CANCEL_MARKET_DATA: REQUEST_TYPES.GET_MARKET_DATA,
    REQUEST_TYPES.CANCEL_HISTORICAL_TICKS: REQUEST_TYPES.GET_HISTORICAL_TICKS,
    REQUEST_TYPES.CANCEL_TICK_BY_TICK_DATA: REQUEST_TYPES.GET_TICK_BY_TICK_DATA,
    REQUEST_TYPES.CANCEL_FUNDAMENTAL_DATA: REQUEST_TYPES.GET_FUNDAMENTAL_DATA,
    REQUEST_TYPES.CANCEL_MARKET_DEPTH: REQUEST_TYPES.GET_MARKET_DEPTH,
    REQUEST_TYPES.CANCEL_MARKET_DEPTH_L2: REQUEST_TYPES.GET_MARKET_DEPTH_L2,
    REQUEST_TYPES.CANCEL_WSH_EVENT_DATA: REQUEST_TYPES.GET_WSH_EVENT_DATA,
    REQUEST_TYPES.CANCEL_WSH_META_DATA: REQUEST_TYPES.GET_WSH_META_DATA,
    REQUEST_TYPES.CANCEL_HEAD_TIME_STAMP: REQUEST_TYPES.GET_HEAD_TIME_STAMP
}

class DelayEvent(Event):
    def __init__(self, timeout: float = 0.0,delay: float = 0.0, callbackWhenFinished = None):
        super().__init__()
        self.delay = delay
        self.timeout = timeout
        self.callbackWhenFinished = callbackWhenFinished
  


    def delay_wait(self):
        result = False

        if self.timeout > 0.0:
            result = super().wait(self.timeout)
          
        else:
            result = super().wait()

       
        time.sleep(self.delay)
        if self.callbackWhenFinished is not None:
            self.callbackWhenFinished(self, result)

        return result



class Request():
    def __init__(self, type : REQUEST_TYPES, reqId: int = -1, keepAlive: bool = False, timeout: float = 0.0, delayWhenFinished: float = 0.0, callbackWhenFinished = None, noResponse: bool = False , parms: dict = {}):

        self.type = type
        self.reqId = reqId
        self.done = DelayEvent(timeout, delayWhenFinished , callbackWhenFinished = self.handle_when_done)
        self.keepAlive = keepAlive
        self.response = Response()
        self.callbackWhenFinished = callbackWhenFinished
        self.isTimeout = False
        self.notRequireResponse =  noResponse
        self.parms = parms
        self.update_callback = None
        self.userData : dict = {}
        
        # most of a reuest with keepalive require to be unsubscribed, identify request with TWS is through reqId, botn to cancel subscribe request and  to subcribe request use the same reqId, so need to make different to identify which request is the one to cancel
        self.pendingDelete = False


    def handle_when_done(self, event: DelayEvent, resultOfWait: bool):
 
        self.isTimeout = not resultOfWait
        if self.isTimeout == True:
            if  self.notRequireResponse == False:
                self.response.errors.append(CLIENT_REQUEST_ERRORS.TIME_OUT)
            

        if self.callbackWhenFinished is not None:
            self.callbackWhenFinished(self)
    


class Response():

    def __init__(self):
        self.contents = []
        self.errors = []
        self.warnings = []
        self.infos = []


class OrderVaildator():
    @staticmethod
    def roundPrice(price: float, minTick: float) -> float:
        _price = Decimal(str(price)) 
        _minTick = Decimal(str(minTick))
        ticks = _price / _minTick
        ticks = ticks.quantize(Decimal('1'), rounding=ROUND_HALF_UP)
        return float(ticks * _minTick)

    @staticmethod   
    def roundQuantity(quantity: Decimal, minSize: Decimal, sizeIncrement: Decimal) -> float:
        if minSize > 0:
            quantity = max(quantity, minSize)
        if sizeIncrement > 0:
            quantity = round(quantity / sizeIncrement) * sizeIncrement
        return float(quantity)




# class DataConverter():
#     @staticmethod
#     def convertContractToDict(contract: Contract):
#         def recursive_convert(item):
#             if isinstance(item, list):
#                 return [recursive_convert(elem) for elem in item]
#             elif isinstance(item, dict):
#                 return {k: recursive_convert(v) for k, v in item.items()}
#             elif hasattr(item, '__dict__'):
#                 # Convert the custom object by accessing its __dict__
#                 return recursive_convert(vars(item))
#             else:
#                 return item
#         return recursive_convert(vars(contract))

#     @staticmethod
#     def convertDictToContract(contract_dict:dict):
#         """
#         Given a dictionary representing a Contract object, create and return a new
#         Contract instance with properties populated from the dictionary.
#         """

#         # Create a new Contract instance.
#         contract = Contract()

#         # Assign basic attributes, using get() for safety.
#         contract.conId = contract_dict.get("conId", 0)
#         contract.symbol = contract_dict.get("symbol", "")
#         contract.secType = contract_dict.get("secType", "")
#         contract.lastTradeDateOrContractMonth = contract_dict.get("lastTradeDateOrContractMonth", "")
#         contract.lastTradeDate = contract_dict.get("lastTradeDate", "")
#         contract.strike = contract_dict.get("strike", UNSET_DOUBLE)
#         contract.right = contract_dict.get("right", "")
#         contract.multiplier = contract_dict.get("multiplier", "")
#         contract.exchange = contract_dict.get("exchange", "")
#         contract.primaryExchange = contract_dict.get("primaryExchange", "")
#         contract.currency = contract_dict.get("currency", "")
#         contract.localSymbol = contract_dict.get("localSymbol", "")
#         contract.tradingClass = contract_dict.get("tradingClass", "")
#         contract.includeExpired = contract_dict.get("includeExpired", False)
#         contract.secIdType = contract_dict.get("secIdType", "")
#         contract.secId = contract_dict.get("secId", "")
#         contract.description = contract_dict.get("description", "")
#         contract.issuerId = contract_dict.get("issuerId", "")
#         contract.comboLegsDescrip = contract_dict.get("comboLegsDescrip", "")

#         # Optionally, if comboLegs is stored as a list of dictionaries,
#         # you might need to convert each one into an instance of ComboLeg (if defined).
#         # For now, we simply assign the list.
#         contract.comboLegs = contract_dict.get("comboLegs", [])

#         # Similarly, assign deltaNeutralContract; if it is a dict, a custom conversion can be applied.
#         contract.deltaNeutralContract = contract_dict.get("deltaNeutralContract", None)
        
#         return contract

#     @staticmethod
#     def convert_bardata_to_dict( bar: BarData):
#         return {
#             "date": DataConverter.convert_time_to_utc(bar.date) if len(bar.date) >8 else f"{bar.date[0:4]}-{bar.date[4:6]}-{bar.date[6:8]}",  #f"{bar.date[0:4]}-{bar.date[4:6]}-{bar.date[6:8]} {bar.date[9:17]}",
#             "open": bar.open,
#             "high": bar.high,
#             "low": bar.low,
#             "close": bar.close,
#             "volume": int(bar.volume),
#             "wap": bar.wap,
#             "barCount": bar.barCount
#         }
    

#     @staticmethod
#     def convert_time_to_utc(s: str):
#         """
#         將 "YYYYMMDD HH:MM:SS TZ" 轉成 UTC timestamp (整數秒)
#         範例輸入："20250623 13:15:00 US/Eastern"
#         """
#         # 切出 datetime 部分和時區字符串
#         dt_part, tz_name = s.rsplit(" ", 1)
#         # 先做 naive datetime
#         dt_naive = datetime.strptime(dt_part, "%Y%m%d %H:%M:%S")
#         # 建立時區物件

#         tz = ZoneInfo.timezone(tz_name)       # pytz 模組

#         # 指定時區
#         dt_loc = dt_naive.replace(tzinfo=tz)
#         # 回傳整數秒
#         return int(dt_loc.timestamp())

