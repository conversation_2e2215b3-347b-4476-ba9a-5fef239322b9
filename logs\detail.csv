Time,Caller,Message
2025-07-25 08:11:06,nextValidId,orderId: 228
2025-07-25 08:11:06,Req reqAccountUpdates,"('',) {}"
2025-07-25 08:11:07,accountDownloadEnd,accountName: DU7492998
2025-07-25 08:11:07,Req reqMarketDataType,"(1,) {}"
2025-07-25 08:11:07,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 08:11:07,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 08:11:07,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 08:11:07,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 08:11:07,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 08:11:07,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 08:11:08,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 08:11:08,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.TSM NYSE/TOP/ALL contract:"
2025-07-25 08:11:08,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-25 08:11:08,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 08:11:08,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 08:11:08,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-25 08:11:08,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-25 08:11:08,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 08:11:08,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-25 08:11:08,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 08:11:08,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-25 08:11:08,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 08:11:08,Req reqMarketDataType,"(3,) {}"
2025-07-25 08:11:09,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 08:11:09,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 08:11:09,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 08:11:09,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-25 08:11:09,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 08:11:09,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 08:11:09,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-25 08:11:09,error,"reqId: 0, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 08:11:09,Req reqHistoricalData,"(6, 2695427594800: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 08:11:10,historicalDataEnd,"reqId: 6, start: ******** 20:11:08 US/Eastern, end: ******** 20:11:08 US/Eastern"
2025-07-25 08:11:10,Req reqHistoricalData,"(7, 2695427595664: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 08:11:11,historicalDataEnd,"reqId: 7, start: ******** 20:11:09 US/Eastern, end: ******** 20:11:09 US/Eastern"
2025-07-25 08:11:11,Req reqHistoricalData,"(8, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 08:11:11,historicalDataEnd,"reqId: 8, start: ******** 09:11:10 Japan, end: ******** 09:11:10 Japan"
2025-07-25 09:06:48,nextValidId,orderId: 228
2025-07-25 09:06:48,Req reqAccountUpdates,"('',) {}"
2025-07-25 09:06:49,accountDownloadEnd,accountName: DU7492998
2025-07-25 09:06:49,Req reqMarketDataType,"(1,) {}"
2025-07-25 09:06:49,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 09:06:49,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 09:06:49,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 09:06:49,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 09:06:49,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 09:06:49,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 09:06:50,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 09:06:50,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.TSM NYSE/TOP/ALL contract:"
2025-07-25 09:06:50,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 09:06:50,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-25 09:06:50,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-25 09:06:50,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 09:06:50,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-25 09:06:50,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 09:06:50,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-25 09:06:50,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 09:06:50,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-25 09:06:50,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 09:06:50,Req reqMarketDataType,"(3,) {}"
2025-07-25 09:06:51,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 09:06:51,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 09:06:51,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 09:06:51,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-25 09:06:51,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 09:06:51,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 09:06:51,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-25 09:06:51,error,"reqId: 0, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 09:06:51,Req reqHistoricalData,"(6, 2258077737968: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 09:06:52,historicalDataEnd,"reqId: 6, start: ******** 21:06:51 US/Eastern, end: ******** 21:06:51 US/Eastern"
2025-07-25 09:06:52,Req reqHistoricalData,"(7, 2258077738880: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 09:06:52,historicalDataEnd,"reqId: 7, start: ******** 21:06:51 US/Eastern, end: ******** 21:06:51 US/Eastern"
2025-07-25 09:06:52,Req reqHistoricalData,"(8, 2258077739696: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 09:06:53,historicalDataEnd,"reqId: 8, start: ******** 10:06:52 Japan, end: ******** 10:06:52 Japan"
2025-07-25 21:26:36,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-25 21:26:40,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-25 21:29:16,nextValidId,orderId: 228
2025-07-25 21:29:16,Req reqAccountUpdates,"('',) {}"
2025-07-25 21:29:16,accountDownloadEnd,accountName: DU7492998
2025-07-25 21:29:17,Req reqMarketDataType,"(1,) {}"
2025-07-25 21:29:17,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:29:17,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:29:17,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:29:17,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:29:17,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:29:17,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:29:17,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 21:29:17,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.TSM NYSE/TOP/ALL contract:"
2025-07-25 21:29:17,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-25 21:29:18,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-25 21:29:18,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:29:18,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-25 21:29:18,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:29:18,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-25 21:29:18,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:29:18,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-25 21:29:18,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:29:18,Req reqMarketDataType,"(3,) {}"
2025-07-25 21:29:19,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:29:19,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:29:19,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-25 21:29:19,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 21:29:19,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 21:29:19,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 21:29:19,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-25 21:29:19,error,"reqId: 0, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 21:29:19,Req reqHistoricalData,"(6, 2048837818928: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 21:29:20,historicalDataEnd,"reqId: 6, start: ******** 09:29:19 US/Eastern, end: ******** 09:29:19 US/Eastern"
2025-07-25 21:29:20,Req reqHistoricalData,"(7, 2048837819888: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 21:29:21,historicalDataEnd,"reqId: 7, start: ******** 09:29:19 US/Eastern, end: ******** 09:29:19 US/Eastern"
2025-07-25 21:29:21,Req reqHistoricalData,"(8, 2048837820560: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 21:29:22,historicalDataEnd,"reqId: 8, start: ******** 22:29:21 Japan, end: ******** 22:29:21 Japan"
2025-07-25 21:29:22,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:29:24,historicalDataEnd,"reqId: 9, start: ******** 09:29:22 US/Eastern, end: ******** 09:29:22 US/Eastern"
2025-07-25 21:29:29,Req cancelHistoricalData,"(9,) {}"
2025-07-25 21:29:29,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-25 21:29:33,Req reqHistoricalData,"() {'reqId': 10, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:29:34,historicalDataEnd,"reqId: 10, start: ******** 09:29:33 US/Eastern, end: ******** 09:29:33 US/Eastern"
2025-07-25 21:29:36,Req cancelHistoricalData,"(10,) {}"
2025-07-25 21:29:36,error,"reqId: 10, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 10 contract:"
2025-07-25 21:29:39,Req reqHistoricalData,"() {'reqId': 11, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 W', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:29:41,historicalDataEnd,"reqId: 11, start: ******** 09:29:39 US/Eastern, end: ******** 09:29:39 US/Eastern"
2025-07-25 21:29:43,Req reqHistoricalData,"() {'reqId': 12, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:29:45,historicalDataEnd,"reqId: 12, start: ******** 09:29:43 US/Eastern, end: ******** 09:29:43 US/Eastern"
2025-07-25 21:29:46,Req cancelHistoricalData,"(11,) {}"
2025-07-25 21:29:46,error,"reqId: 11, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 11 contract:"
2025-07-25 21:29:49,Req cancelHistoricalData,"(12,) {}"
2025-07-25 21:29:49,error,"reqId: 12, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 12 contract:"
2025-07-25 21:31:44,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.hfarm contract:"
2025-07-25 21:31:44,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.hfarm contract:"
2025-07-25 21:31:44,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.usfuture contract:"
2025-07-25 21:31:44,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.usfuture contract:"
2025-07-25 21:31:44,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.eufarmnj contract:"
2025-07-25 21:31:44,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.eufarmnj contract:"
2025-07-25 21:32:56,Req reqHistoricalData,"() {'reqId': 13, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:32:58,historicalDataEnd,"reqId: 13, start: ******** 09:32:56 US/Eastern, end: ******** 09:32:56 US/Eastern"
2025-07-25 21:32:58,Req cancelHistoricalData,"(13,) {}"
2025-07-25 21:32:58,error,"reqId: 13, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 13 contract:"
2025-07-25 21:44:45,nextValidId,orderId: 228
2025-07-25 21:44:45,Req reqAccountUpdates,"('',) {}"
2025-07-25 21:44:45,accountDownloadEnd,accountName: DU7492998
2025-07-25 21:44:46,Req reqMarketDataType,"(1,) {}"
2025-07-25 21:44:46,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:44:46,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:44:46,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:44:46,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:44:46,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:44:46,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:44:46,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 21:44:46,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.TSM NYSE/TOP/ALL contract:"
2025-07-25 21:44:46,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 21:44:46,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-25 21:44:47,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-25 21:44:47,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:44:47,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-25 21:44:47,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:44:47,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-25 21:44:47,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:44:47,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-25 21:44:47,Req reqMarketDataType,"(3,) {}"
2025-07-25 21:44:47,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:44:47,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:44:47,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:44:48,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 21:44:48,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-25 21:44:48,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 21:44:48,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 21:44:48,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-25 21:44:48,error,"reqId: 0, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 21:44:48,Req reqHistoricalData,"(6, 2028401269296: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 21:44:49,historicalDataEnd,"reqId: 6, start: ******** 09:44:48 US/Eastern, end: ******** 09:44:48 US/Eastern"
2025-07-25 21:44:49,Req reqHistoricalData,"(7, 2028401270304: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 21:44:50,historicalDataEnd,"reqId: 7, start: ******** 09:44:49 US/Eastern, end: ******** 09:44:49 US/Eastern"
2025-07-25 21:44:50,Req reqHistoricalData,"(8, 2028401271024: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 21:44:50,historicalDataEnd,"reqId: 8, start: ******** 22:44:49 Japan, end: ******** 22:44:49 Japan"
2025-07-25 21:44:50,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:44:52,historicalDataEnd,"reqId: 9, start: ******** 09:44:50 US/Eastern, end: ******** 09:44:50 US/Eastern"
2025-07-25 21:44:52,Req cancelHistoricalData,"(9,) {}"
2025-07-25 21:44:52,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-25 21:44:57,Req reqHistoricalData,"() {'reqId': 10, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:44:58,historicalDataEnd,"reqId: 10, start: ******** 09:44:56 US/Eastern, end: ******** 09:44:56 US/Eastern"
2025-07-25 21:44:59,Req cancelHistoricalData,"(10,) {}"
2025-07-25 21:44:59,error,"reqId: 10, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 10 contract:"
2025-07-25 21:45:02,Req reqHistoricalData,"() {'reqId': 11, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 W', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:45:03,historicalDataEnd,"reqId: 11, start: ******** 09:45:02 US/Eastern, end: ******** 09:45:02 US/Eastern"
2025-07-25 21:45:34,nextValidId,orderId: 228
2025-07-25 21:45:34,Req reqAccountUpdates,"('',) {}"
2025-07-25 21:45:34,accountDownloadEnd,accountName: DU7492998
2025-07-25 21:45:35,Req reqMarketDataType,"(1,) {}"
2025-07-25 21:45:35,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:45:35,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:45:35,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:45:35,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:45:35,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:45:35,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:45:35,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-25 21:45:35,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:45:35,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 21:45:35,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-25 21:45:35,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 21:45:35,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.TSM NYSE/TOP/ALL contract:"
2025-07-25 21:45:35,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-25 21:45:35,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:45:36,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-25 21:45:36,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:45:36,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-25 21:45:36,Req reqMarketDataType,"(3,) {}"
2025-07-25 21:45:36,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 21:45:36,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:45:36,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 21:45:36,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 21:45:36,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-25 21:45:36,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 21:45:36,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 21:45:36,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-25 21:45:36,error,"reqId: 0, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 21:45:51,Req reqHistoricalData,"(6, 2358931364496: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 21:45:52,historicalDataEnd,"reqId: 6, start: ******** 09:45:50 US/Eastern, end: ******** 09:45:50 US/Eastern"
2025-07-25 21:45:52,Req reqHistoricalData,"(7, 2358931365552: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 21:45:53,historicalDataEnd,"reqId: 7, start: ******** 09:45:51 US/Eastern, end: ******** 09:45:51 US/Eastern"
2025-07-25 21:45:53,Req reqHistoricalData,"(8, 2358931366320: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 21:45:53,historicalDataEnd,"reqId: 8, start: ******** 22:45:52 Japan, end: ******** 22:45:52 Japan"
2025-07-25 21:46:06,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:46:07,historicalDataEnd,"reqId: 9, start: ******** 09:46:05 US/Eastern, end: ******** 09:46:05 US/Eastern"
2025-07-25 21:46:08,Req cancelHistoricalData,"(9,) {}"
2025-07-25 21:46:08,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-25 21:46:11,Req reqHistoricalData,"() {'reqId': 10, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:46:12,historicalDataEnd,"reqId: 10, start: ******** 09:46:10 US/Eastern, end: ******** 09:46:10 US/Eastern"
2025-07-25 21:46:12,Req cancelHistoricalData,"(10,) {}"
2025-07-25 21:46:12,error,"reqId: 10, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 10 contract:"
2025-07-25 21:46:15,Req reqHistoricalData,"() {'reqId': 11, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 W', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:46:16,historicalDataEnd,"reqId: 11, start: ******** 09:46:14 US/Eastern, end: ******** 09:46:14 US/Eastern"
2025-07-25 21:46:18,Req reqHistoricalData,"() {'reqId': 12, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '5 Y', 'barSizeSetting': '1 week', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:46:19,historicalDataEnd,"reqId: 12, start: 20200725 09:46:17 US/Eastern, end: ******** 09:46:17 US/Eastern"
2025-07-25 21:46:19,Req reqHistoricalData,"() {'reqId': 13, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:46:20,error,"reqId: 13, errorCode: 162, errorString: Historical Market Data Service error message:HMDS query returned no data: TSM@SMART Trades contract:"
2025-07-25 21:46:21,Req reqHistoricalData,"() {'reqId': 13, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:46:22,historicalDataEnd,"reqId: 13, start: ******** 09:46:20 US/Eastern, end: ******** 09:46:20 US/Eastern"
2025-07-25 21:46:27,Req cancelHistoricalData,"(13,) {}"
2025-07-25 21:46:27,error,"reqId: 13, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 13 contract:"
2025-07-25 21:46:29,Req reqHistoricalData,"() {'reqId': 14, 'contract': *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:46:30,historicalDataEnd,"reqId: 14, start: ******** 09:46:28 US/Eastern, end: ******** 09:46:28 US/Eastern"
2025-07-25 21:46:30,Req cancelHistoricalData,"(12,) {}"
2025-07-25 21:46:30,error,"reqId: 12, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 12 contract:"
2025-07-25 21:46:32,Req reqHistoricalData,"() {'reqId': 15, 'contract': *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '5 Y', 'barSizeSetting': '1 week', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:46:33,historicalDataEnd,"reqId: 15, start: 20200725 09:46:31 US/Eastern, end: ******** 09:46:31 US/Eastern"
2025-07-25 21:46:33,Req cancelHistoricalData,"(11,) {}"
2025-07-25 21:46:33,error,"reqId: 11, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 11 contract:"
2025-07-25 21:46:35,Req reqHistoricalData,"() {'reqId': 16, 'contract': *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 W', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:46:36,historicalDataEnd,"reqId: 16, start: ******** 09:46:34 US/Eastern, end: ******** 09:46:34 US/Eastern"
2025-07-25 21:46:36,Req reqHistoricalData,"() {'reqId': 17, 'contract': *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:46:37,historicalDataEnd,"reqId: 17, start: ******** 09:41:35 US/Eastern, end: ******** 09:46:35 US/Eastern"
2025-07-25 21:47:59,Req cancelHistoricalData,"(14,) {}"
2025-07-25 21:47:59,error,"reqId: 14, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 14 contract:"
2025-07-25 21:48:02,Req reqHistoricalData,"() {'reqId': 18, 'contract': *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:48:02,historicalDataEnd,"reqId: 18, start: ******** 09:48:01 US/Eastern, end: ******** 09:48:01 US/Eastern"
2025-07-25 21:48:03,Req cancelHistoricalData,"(18,) {}"
2025-07-25 21:48:03,error,"reqId: 18, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 18 contract:"
2025-07-25 21:48:06,Req reqHistoricalData,"() {'reqId': 19, 'contract': *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 21:48:07,historicalDataEnd,"reqId: 19, start: ******** 09:48:05 US/Eastern, end: ******** 09:48:05 US/Eastern"
2025-07-25 21:48:08,Req cancelHistoricalData,"(19,) {}"
2025-07-25 21:48:08,error,"reqId: 19, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 19 contract:"
2025-07-25 21:48:18,Req cancelHistoricalData,"(17,) {}"
2025-07-25 21:48:18,error,"reqId: 17, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 17 contract:"
2025-07-25 22:24:52,nextValidId,orderId: 228
2025-07-25 22:24:52,Req reqAccountUpdates,"('',) {}"
2025-07-25 22:24:52,accountDownloadEnd,accountName: DU7492998
2025-07-25 22:24:52,Req reqMarketDataType,"(1,) {}"
2025-07-25 22:24:53,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:24:53,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:24:53,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:24:53,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:24:53,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:24:53,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:24:53,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 22:24:53,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.TSM NYSE/TOP/ALL contract:"
2025-07-25 22:24:53,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 22:24:53,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-25 22:24:53,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-25 22:24:53,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 22:24:54,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-25 22:24:54,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 22:24:54,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-25 22:24:54,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 22:24:54,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-25 22:24:54,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 22:24:54,Req reqMarketDataType,"(3,) {}"
2025-07-25 22:24:54,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:24:54,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:24:55,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 22:24:55,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-25 22:24:55,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 22:24:55,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 22:24:55,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-25 22:24:55,error,"reqId: 0, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 22:24:55,Req reqHistoricalData,"(6, 2337369349328: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 22:24:56,historicalDataEnd,"reqId: 6, start: ******** 10:24:54 US/Eastern, end: ******** 10:24:54 US/Eastern"
2025-07-25 22:24:56,Req reqHistoricalData,"(7, 2337369350432: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 22:24:57,historicalDataEnd,"reqId: 7, start: ******** 10:24:55 US/Eastern, end: ******** 10:24:55 US/Eastern"
2025-07-25 22:24:57,Req reqHistoricalData,"(8, 2337369351248: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 22:24:57,historicalDataEnd,"reqId: 8, start: ******** 23:24:56 Japan, end: ******** 23:24:56 Japan"
2025-07-25 22:25:10,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:25:11,historicalDataEnd,"reqId: 9, start: ******** 10:25:10 US/Eastern, end: ******** 10:25:10 US/Eastern"
2025-07-25 22:25:13,Req cancelHistoricalData,"(9,) {}"
2025-07-25 22:25:26,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-25 22:25:48,Req reqHistoricalData,"() {'reqId': 10, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 W', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:25:49,historicalDataEnd,"reqId: 10, start: ******** 10:25:47 US/Eastern, end: ******** 10:25:47 US/Eastern"
2025-07-25 22:25:53,Req reqHistoricalData,"() {'reqId': 11, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:25:54,historicalDataEnd,"reqId: 11, start: ******** 10:25:52 US/Eastern, end: ******** 10:25:52 US/Eastern"
2025-07-25 22:25:56,Req cancelHistoricalData,"(10,) {}"
2025-07-25 22:26:12,error,"reqId: 10, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 10 contract:"
2025-07-25 22:26:51,Req reqHistoricalData,"() {'reqId': 12, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:26:52,error,"reqId: 12, errorCode: 162, errorString: Historical Market Data Service error message:HMDS query returned no data: TSM@SMART Trades contract:"
2025-07-25 22:28:10,Req cancelHistoricalData,"(11,) {}"
2025-07-25 22:32:44,error,"reqId: 11, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 11 contract:"
2025-07-25 22:32:46,Req reqHistoricalData,"() {'reqId': 12, 'contract': *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:32:51,historicalDataEnd,"reqId: 12, start: ******** 10:32:45 US/Eastern, end: ******** 10:32:45 US/Eastern"
2025-07-25 22:32:51,Req reqHistoricalData,"() {'reqId': 13, 'contract': *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:32:52,historicalDataEnd,"reqId: 13, start: ******** 10:27:50 US/Eastern, end: ******** 10:32:50 US/Eastern"
2025-07-25 22:37:17,Req cancelHistoricalData,"(12,) {}"
2025-07-25 22:37:27,error,"reqId: 12, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 12 contract:"
2025-07-25 22:37:29,Req reqHistoricalData,"() {'reqId': 14, 'contract': *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:37:30,historicalDataEnd,"reqId: 14, start: ******** 10:37:28 US/Eastern, end: ******** 10:37:28 US/Eastern"
2025-07-25 22:37:31,Req cancelHistoricalData,"(13,) {}"
2025-07-25 22:37:37,error,"reqId: 13, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 13 contract:"
2025-07-25 22:37:39,Req reqHistoricalData,"() {'reqId': 15, 'contract': *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:37:39,historicalDataEnd,"reqId: 15, start: ******** 10:32:38 US/Eastern, end: ******** 10:37:38 US/Eastern"
2025-07-25 22:41:12,Req cancelHistoricalData,"(14,) {}"
2025-07-25 22:41:25,error,"reqId: 14, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 14 contract:"
2025-07-25 22:41:26,Req reqHistoricalData,"() {'reqId': 16, 'contract': *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:41:28,historicalDataEnd,"reqId: 16, start: ******** 10:41:25 US/Eastern, end: ******** 10:41:25 US/Eastern"
2025-07-25 22:41:28,Req cancelHistoricalData,"(15,) {}"
2025-07-25 22:41:49,error,"reqId: 15, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 15 contract:"
2025-07-25 22:41:51,Req reqHistoricalData,"() {'reqId': 17, 'contract': *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:41:52,historicalDataEnd,"reqId: 17, start: ******** 10:36:50 US/Eastern, end: ******** 10:41:50 US/Eastern"
2025-07-25 22:44:46,Req cancelHistoricalData,"(16,) {}"
2025-07-25 22:44:52,error,"reqId: 16, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 16 contract:"
2025-07-25 22:54:23,nextValidId,orderId: 228
2025-07-25 22:54:23,Req reqAccountUpdates,"('',) {}"
2025-07-25 22:54:24,accountDownloadEnd,accountName: DU7492998
2025-07-25 22:54:24,Req reqMarketDataType,"(1,) {}"
2025-07-25 22:54:24,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:54:24,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:54:24,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:54:24,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:54:24,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:54:24,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:54:25,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 22:54:25,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.TSM NYSE/TOP/ALL contract:"
2025-07-25 22:54:25,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 22:54:25,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-25 22:54:25,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-25 22:54:25,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 22:54:25,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-25 22:54:25,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 22:54:25,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-25 22:54:25,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 22:54:25,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-25 22:54:25,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 22:54:25,Req reqMarketDataType,"(3,) {}"
2025-07-25 22:54:26,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:54:26,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 22:54:26,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 22:54:26,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-25 22:54:26,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 22:54:26,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 22:54:26,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-25 22:54:26,error,"reqId: 0, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 22:54:26,Req reqHistoricalData,"(6, 1940535680448: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 22:54:27,historicalDataEnd,"reqId: 6, start: ******** 10:54:25 US/Eastern, end: ******** 10:54:25 US/Eastern"
2025-07-25 22:54:27,Req reqHistoricalData,"(7, 1940535681600: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 22:54:28,historicalDataEnd,"reqId: 7, start: ******** 10:54:26 US/Eastern, end: ******** 10:54:26 US/Eastern"
2025-07-25 22:54:28,Req reqHistoricalData,"(8, 1940535682464: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 22:54:28,historicalDataEnd,"reqId: 8, start: ******** 23:54:26 Japan, end: ******** 23:54:26 Japan"
2025-07-25 22:54:28,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:54:29,historicalDataEnd,"reqId: 9, start: ******** 10:54:27 US/Eastern, end: ******** 10:54:27 US/Eastern"
2025-07-25 22:54:33,Req reqHistoricalData,"() {'reqId': 10, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:54:34,error,"reqId: 10, errorCode: 162, errorString: Historical Market Data Service error message:HMDS query returned no data: TSM@SMART Trades contract:"
2025-07-25 22:54:39,Req cancelHistoricalData,"(9,) {}"
2025-07-25 22:54:56,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-25 22:54:58,Req reqHistoricalData,"() {'reqId': 10, 'contract': *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:54:59,historicalDataEnd,"reqId: 10, start: ******** 10:54:57 US/Eastern, end: ******** 10:54:57 US/Eastern"
2025-07-25 22:54:59,Req reqHistoricalData,"() {'reqId': 11, 'contract': *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 22:55:00,historicalDataEnd,"reqId: 11, start: ******** 10:49:58 US/Eastern, end: ******** 10:54:58 US/Eastern"
2025-07-25 22:57:48,nextValidId,orderId: 228
2025-07-25 22:58:11,Req reqAccountUpdates,"('',) {}"
2025-07-25 22:58:11,accountDownloadEnd,accountName: DU7492998
2025-07-25 23:00:17,Req reqMarketDataType,"(1,) {}"
2025-07-25 23:01:19,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:01:19,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:01:19,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:01:19,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:01:19,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:01:19,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:01:19,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 23:01:19,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-25 23:01:21,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 23:01:21,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.TSM NYSE/TOP/ALL contract:"
2025-07-25 23:01:21,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-25 23:01:21,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 23:01:21,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-25 23:01:21,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 23:01:21,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-25 23:01:21,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 23:01:21,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-25 23:01:21,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 23:17:56,nextValidId,orderId: 228
2025-07-25 23:17:56,Req reqAccountUpdates,"('',) {}"
2025-07-25 23:17:57,accountDownloadEnd,accountName: DU7492998
2025-07-25 23:17:57,Req reqMarketDataType,"(1,) {}"
2025-07-25 23:17:57,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:17:57,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:17:57,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:17:57,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:17:57,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 23:17:57,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:17:57,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-25 23:17:57,Req reqMktData,"(5, 2176010828304: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:17:57,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 23:17:58,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 23:17:58,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-25 23:17:58,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 23:17:58,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 23:17:58,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-25 23:17:58,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-25 23:17:58,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-25 23:17:58,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-25 23:17:58,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-25 23:17:58,Req reqMarketDataType,"(3,) {}"
2025-07-25 23:17:59,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:17:59,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-25 23:17:59,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-25 23:17:59,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-25 23:17:59,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-25 23:17:59,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-25 23:17:59,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-25 23:17:59,Req reqHistoricalData,"(0, 2176010886064: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 23:18:00,historicalDataEnd,"reqId: 0, start: ******** 11:17:59 US/Eastern, end: ******** 11:17:59 US/Eastern"
2025-07-25 23:18:00,Req reqHistoricalData,"(6, 2176010887264: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 23:18:00,historicalDataEnd,"reqId: 6, start: ******** 11:17:59 US/Eastern, end: ******** 11:17:59 US/Eastern"
2025-07-25 23:18:00,Req reqHistoricalData,"(7, 2176010888176: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-25 23:18:01,historicalDataEnd,"reqId: 7, start: ******** 00:18:00 Japan, end: ******** 00:18:00 Japan"
2025-07-25 23:18:09,Req reqHistoricalData,"() {'reqId': 8, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 W', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-25 23:18:10,historicalDataEnd,"reqId: 8, start: ******** 11:18:08 US/Eastern, end: ******** 11:18:08 US/Eastern"
2025-07-25 23:19:00,Req cancelHistoricalData,"(8,) {}"
2025-07-26 01:57:01,nextValidId,orderId: 228
2025-07-26 01:57:01,Req reqAccountUpdates,"('',) {}"
2025-07-26 01:57:01,accountDownloadEnd,accountName: DU7492998
2025-07-26 01:57:02,Req reqMarketDataType,"(1,) {}"
2025-07-26 01:57:02,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 01:57:02,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 01:57:02,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 01:57:02,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 01:57:02,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 01:57:02,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 01:57:02,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 01:57:02,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 01:57:02,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 01:57:02,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 01:57:03,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 01:57:03,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 01:57:03,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 01:57:03,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 01:57:03,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 01:57:03,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 01:57:03,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 01:57:03,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 01:57:03,Req reqMarketDataType,"(3,) {}"
2025-07-26 01:57:04,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 01:57:04,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 01:57:04,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 01:57:04,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 01:57:04,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 01:57:04,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 01:57:04,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 01:57:04,Req reqHistoricalData,"(0, 2391673871712: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 01:57:05,historicalDataEnd,"reqId: 0, start: ******** 13:57:03 US/Eastern, end: ******** 13:57:03 US/Eastern"
2025-07-26 01:57:05,Req reqHistoricalData,"(6, 2391673872960: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 01:57:05,historicalDataEnd,"reqId: 6, start: ******** 13:57:04 US/Eastern, end: ******** 13:57:04 US/Eastern"
2025-07-26 01:57:05,Req reqHistoricalData,"(7, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 01:57:06,historicalDataEnd,"reqId: 7, start: ******** 02:57:05 Japan, end: ******** 02:57:05 Japan"
2025-07-26 02:00:44,nextValidId,orderId: 228
2025-07-26 02:00:44,Req reqAccountUpdates,"('',) {}"
2025-07-26 02:00:44,accountDownloadEnd,accountName: DU7492998
2025-07-26 02:00:44,Req reqMarketDataType,"(1,) {}"
2025-07-26 02:00:45,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:00:45,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:00:45,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:00:45,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:00:45,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:00:45,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:00:45,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 02:00:45,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 02:04:56,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 02:04:56,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 02:04:56,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 02:04:56,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 02:04:56,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 02:04:56,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 02:04:56,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 02:04:56,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 02:04:56,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 02:04:56,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 02:04:56,Req reqMarketDataType,"(3,) {}"
2025-07-26 02:04:57,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:04:57,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:17:15,nextValidId,orderId: 228
2025-07-26 02:17:15,Req reqAccountUpdates,"('',) {}"
2025-07-26 02:17:15,accountDownloadEnd,accountName: DU7492998
2025-07-26 02:17:15,Req reqMarketDataType,"(1,) {}"
2025-07-26 02:17:16,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:17:16,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:17:16,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:17:16,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:17:16,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:17:16,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:17:16,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 02:17:16,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 02:25:42,nextValidId,orderId: 228
2025-07-26 02:25:42,Req reqAccountUpdates,"('',) {}"
2025-07-26 02:25:43,accountDownloadEnd,accountName: DU7492998
2025-07-26 02:25:43,Req reqMarketDataType,"(1,) {}"
2025-07-26 02:25:43,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:25:43,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:25:43,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:25:43,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:25:43,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:25:43,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:25:43,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 02:25:43,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 02:25:43,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 02:26:36,nextValidId,orderId: 228
2025-07-26 02:26:36,Req reqAccountUpdates,"('',) {}"
2025-07-26 02:26:37,accountDownloadEnd,accountName: DU7492998
2025-07-26 02:26:37,Req reqMarketDataType,"(1,) {}"
2025-07-26 02:26:37,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:27:06,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:27:06,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:27:06,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:27:06,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:27:06,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 02:27:06,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:27:06,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 02:41:03,nextValidId,orderId: 228
2025-07-26 02:41:03,Req reqAccountUpdates,"('',) {}"
2025-07-26 02:41:04,accountDownloadEnd,accountName: DU7492998
2025-07-26 02:41:04,Req reqMarketDataType,"(1,) {}"
2025-07-26 02:41:04,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:41:38,nextValidId,orderId: 228
2025-07-26 02:41:38,Req reqAccountUpdates,"('',) {}"
2025-07-26 02:41:38,accountDownloadEnd,accountName: DU7492998
2025-07-26 02:41:38,Req reqMarketDataType,"(1,) {}"
2025-07-26 02:41:39,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:41:39,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:41:39,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:41:39,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 02:41:39,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:41:39,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 02:41:39,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:41:39,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:49:48,nextValidId,orderId: 228
2025-07-26 02:51:22,nextValidId,orderId: 228
2025-07-26 02:52:14,nextValidId,orderId: 228
2025-07-26 02:52:14,Req reqAccountUpdates,"('',) {}"
2025-07-26 02:52:14,accountDownloadEnd,accountName: DU7492998
2025-07-26 02:52:15,Req reqMarketDataType,"(1,) {}"
2025-07-26 02:52:15,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:52:15,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:52:15,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:52:15,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:52:15,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:52:15,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 02:52:15,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 02:52:15,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 02:52:15,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 02:52:15,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 02:52:15,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 03:05:40,nextValidId,orderId: 228
2025-07-26 03:05:40,Req reqAccountUpdates,"('',) {}"
2025-07-26 03:05:41,accountDownloadEnd,accountName: DU7492998
2025-07-26 03:05:41,Req reqMarketDataType,"(1,) {}"
2025-07-26 03:05:41,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 03:05:41,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 03:05:41,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 03:05:41,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 03:05:41,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 03:05:41,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 03:05:41,Req reqMktData,"(3, ***********76: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 03:05:41,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 03:05:41,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 03:05:41,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 03:05:41,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 03:16:20,nextValidId,orderId: 228
2025-07-26 03:16:20,Req reqAccountUpdates,"('',) {}"
2025-07-26 03:16:21,accountDownloadEnd,accountName: DU7492998
2025-07-26 03:16:21,Req reqMarketDataType,"(1,) {}"
2025-07-26 03:16:21,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 03:16:21,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 03:16:21,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 03:16:21,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 03:16:21,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 03:16:21,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 03:16:21,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 03:16:21,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 04:31:48,nextValidId,orderId: 228
2025-07-26 04:31:48,Req reqAccountUpdates,"('',) {}"
2025-07-26 04:31:49,accountDownloadEnd,accountName: DU7492998
2025-07-26 04:31:49,Req reqMarketDataType,"(1,) {}"
2025-07-26 04:31:49,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 04:31:49,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 04:31:49,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 04:31:49,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 04:31:49,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 04:31:49,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 04:31:49,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 04:31:49,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 05:38:08,nextValidId,orderId: 228
2025-07-26 05:38:08,Req reqAccountUpdates,"('',) {}"
2025-07-26 05:38:09,accountDownloadEnd,accountName: DU7492998
2025-07-26 05:38:09,Req reqMarketDataType,"(1,) {}"
2025-07-26 05:38:10,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 05:38:10,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 05:38:10,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 05:38:10,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 05:38:10,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 05:38:10,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 05:38:10,Req reqMktData,"(4, 2478586377040: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 05:38:10,Req reqMktData,"(5, 2478586378288: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 05:39:37,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 05:39:37,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 05:39:37,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 05:39:37,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 05:39:37,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 05:39:37,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 05:39:37,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 05:39:37,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 05:39:37,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 05:39:37,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 05:39:37,Req reqMarketDataType,"(3,) {}"
2025-07-26 05:39:38,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 05:39:38,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 05:39:38,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 05:39:38,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:12:57,nextValidId,orderId: 228
2025-07-26 07:12:57,Req reqAccountUpdates,"('',) {}"
2025-07-26 07:12:57,accountDownloadEnd,accountName: DU7492998
2025-07-26 07:12:57,Req reqMarketDataType,"(1,) {}"
2025-07-26 07:12:58,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:12:58,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:12:58,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:12:58,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:12:58,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:12:58,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:12:58,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:12:58,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:13:07,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:13:07,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 07:13:07,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:13:07,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 07:13:07,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 07:13:07,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:13:07,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 07:13:07,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:13:07,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 07:13:07,Req reqMarketDataType,"(3,) {}"
2025-07-26 07:13:07,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:13:08,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:13:08,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:13:08,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:13:08,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:13:08,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:13:08,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 07:13:08,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 07:13:08,Req reqHistoricalData,"(0, 2704506937296: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:13:09,historicalDataEnd,"reqId: 0, start: ******** 19:13:08 US/Eastern, end: ******** 19:13:08 US/Eastern"
2025-07-26 07:13:09,Req reqHistoricalData,"(6, 2704506938592: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:13:10,historicalDataEnd,"reqId: 6, start: ******** 19:13:09 US/Eastern, end: ******** 19:13:09 US/Eastern"
2025-07-26 07:13:10,Req reqHistoricalData,"(7, 2704506939600: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:13:13,historicalDataEnd,"reqId: 7, start: ******** 08:13:10 Japan, end: ******** 08:13:10 Japan"
2025-07-26 07:34:32,Req reqContractDetails,"(8, 2704507174352: 0,TSM,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:) {}"
2025-07-26 07:34:32,contractDetails,"reqId: 8, contractDetails: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:,TSM,0.01,ACTIVETIM,AD,ADDONT,ADJUST,ALERT,ALGO,ALLOC,AON,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DARKONLY,DARKPOLL,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,DUR,GAT,GTC,GTD,GTT,HID,IBKRATS,ICE,IMB,IOC,LIT,LMT,LOC,MIDPX,MIT,MKT,MOC,MTL,NGCOMB,NODARK,NONALGO,OCA,OPG,OPGREROUT,PEGBENCH,PEGMID,POSTATS,POSTONLY,PREOPGRTH,PRICECHK,REL,REL2MID,RELPCTOFS,RPI,RTH,RTHIGNOPG,SCALE,SCALEODD,SCALERST,SIZECHK,SMARTSTG,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,SWEEP,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,SMART,AMEX,NYSE,CBOE,PHLX,ISE,CHX,ARCA,NASDAQ,DRCTEDGE,BEX,BATS,EDGEA,BYX,IEX,EDGX,FOXRIVER,PEARL,NYSENAT,LTSE,MEMX,IBEOS,OVERNIGHT,TPLUS0,PSX,1,0,TAIWAN SEMICONDUCTOR-SP ADR,,Technology,Semiconductors,Semicon Compo-Intg Circu,US/Eastern,********:0400-********:2000;********:CLOSED;********:CLOSED;********:0400-********:2000;********:0400-********:2000;********:0400-********:2000,********:0930-********:1600;********:CLOSED;********:CLOSED;********:0930-********:1600;********:0930-********:1600;********:0930-********:1600,,0,,,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,1,[*************: ISIN=US8740391003;],,ADR,,,,,,False,False,0,False,,,,,False,,0.0001,0.0001,100,None"
2025-07-26 07:34:32,contractDetailsEnd,reqId: 8
2025-07-26 07:36:03,nextValidId,orderId: 228
2025-07-26 07:36:03,Req reqAccountUpdates,"('',) {}"
2025-07-26 07:36:04,accountDownloadEnd,accountName: DU7492998
2025-07-26 07:36:04,Req reqMarketDataType,"(1,) {}"
2025-07-26 07:36:04,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:04,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:04,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:04,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:04,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:04,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:04,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:36:04,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:36:04,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:36:04,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:36:04,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 07:36:05,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 07:36:05,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:36:05,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:36:05,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 07:36:05,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 07:36:05,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:36:05,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 07:36:05,Req reqMarketDataType,"(3,) {}"
2025-07-26 07:36:06,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:06,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:06,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:36:06,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:36:06,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:36:06,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 07:36:06,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 07:36:06,Req reqHistoricalData,"(0, 2307222527856: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:36:07,historicalDataEnd,"reqId: 0, start: ******** 19:36:06 US/Eastern, end: ******** 19:36:06 US/Eastern"
2025-07-26 07:36:07,Req reqHistoricalData,"(6, 2307222529200: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:36:07,historicalDataEnd,"reqId: 6, start: ******** 19:36:07 US/Eastern, end: ******** 19:36:07 US/Eastern"
2025-07-26 07:36:07,Req reqHistoricalData,"(7, 2307222530256: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:36:08,historicalDataEnd,"reqId: 7, start: ******** 08:36:07 Japan, end: ******** 08:36:07 Japan"
2025-07-26 07:36:14,Req reqContractDetails,"(8, 2307291156688: 0,TSM,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:) {}"
2025-07-26 07:36:14,contractDetails,"reqId: 8, contractDetails: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:,TSM,0.01,ACTIVETIM,AD,ADDONT,ADJUST,ALERT,ALGO,ALLOC,AON,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DARKONLY,DARKPOLL,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,DUR,GAT,GTC,GTD,GTT,HID,IBKRATS,ICE,IMB,IOC,LIT,LMT,LOC,MIDPX,MIT,MKT,MOC,MTL,NGCOMB,NODARK,NONALGO,OCA,OPG,OPGREROUT,PEGBENCH,PEGMID,POSTATS,POSTONLY,PREOPGRTH,PRICECHK,REL,REL2MID,RELPCTOFS,RPI,RTH,RTHIGNOPG,SCALE,SCALEODD,SCALERST,SIZECHK,SMARTSTG,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,SWEEP,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,SMART,AMEX,NYSE,CBOE,PHLX,ISE,CHX,ARCA,NASDAQ,DRCTEDGE,BEX,BATS,EDGEA,BYX,IEX,EDGX,FOXRIVER,PEARL,NYSENAT,LTSE,MEMX,IBEOS,OVERNIGHT,TPLUS0,PSX,1,0,TAIWAN SEMICONDUCTOR-SP ADR,,Technology,Semiconductors,Semicon Compo-Intg Circu,US/Eastern,********:0400-********:2000;********:CLOSED;********:CLOSED;********:0400-********:2000;********:0400-********:2000;********:0400-********:2000,********:0930-********:1600;********:CLOSED;********:CLOSED;********:0930-********:1600;********:0930-********:1600;********:0930-********:1600,,0,,,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,1,[*************: ISIN=US8740391003;],,ADR,,,,,,False,False,0,False,,,,,False,,0.0001,0.0001,100,None"
2025-07-26 07:36:14,contractDetailsEnd,reqId: 8
2025-07-26 07:36:38,nextValidId,orderId: 228
2025-07-26 07:36:39,Req reqAccountUpdates,"('',) {}"
2025-07-26 07:36:39,accountDownloadEnd,accountName: DU7492998
2025-07-26 07:36:39,Req reqMarketDataType,"(1,) {}"
2025-07-26 07:36:40,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:40,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:40,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:40,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:40,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:40,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:40,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:36:40,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:36:40,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:36:40,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:36:40,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 07:36:40,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 07:36:40,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:36:40,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:36:40,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 07:36:40,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:36:40,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 07:36:41,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 07:36:41,Req reqMarketDataType,"(3,) {}"
2025-07-26 07:36:41,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:41,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:36:41,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:36:41,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:36:41,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:36:41,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 07:36:41,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 07:36:48,Req reqContractDetails,"(0, 2474726071360: 0,TSM,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:) {}"
2025-07-26 07:36:48,contractDetails,"reqId: 0, contractDetails: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:,TSM,0.01,ACTIVETIM,AD,ADDONT,ADJUST,ALERT,ALGO,ALLOC,AON,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DARKONLY,DARKPOLL,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,DUR,GAT,GTC,GTD,GTT,HID,IBKRATS,ICE,IMB,IOC,LIT,LMT,LOC,MIDPX,MIT,MKT,MOC,MTL,NGCOMB,NODARK,NONALGO,OCA,OPG,OPGREROUT,PEGBENCH,PEGMID,POSTATS,POSTONLY,PREOPGRTH,PRICECHK,REL,REL2MID,RELPCTOFS,RPI,RTH,RTHIGNOPG,SCALE,SCALEODD,SCALERST,SIZECHK,SMARTSTG,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,SWEEP,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,SMART,AMEX,NYSE,CBOE,PHLX,ISE,CHX,ARCA,NASDAQ,DRCTEDGE,BEX,BATS,EDGEA,BYX,IEX,EDGX,FOXRIVER,PEARL,NYSENAT,LTSE,MEMX,IBEOS,OVERNIGHT,TPLUS0,PSX,1,0,TAIWAN SEMICONDUCTOR-SP ADR,,Technology,Semiconductors,Semicon Compo-Intg Circu,US/Eastern,********:0400-********:2000;********:CLOSED;********:CLOSED;********:0400-********:2000;********:0400-********:2000;********:0400-********:2000,********:0930-********:1600;********:CLOSED;********:CLOSED;********:0930-********:1600;********:0930-********:1600;********:0930-********:1600,,0,,,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,1,[*************: ISIN=US8740391003;],,ADR,,,,,,False,False,0,False,,,,,False,,0.0001,0.0001,100,None"
2025-07-26 07:36:48,contractDetailsEnd,reqId: 0
2025-07-26 07:41:38,nextValidId,orderId: 228
2025-07-26 07:41:38,Req reqAccountUpdates,"('',) {}"
2025-07-26 07:41:39,accountDownloadEnd,accountName: DU7492998
2025-07-26 07:41:39,Req reqMarketDataType,"(1,) {}"
2025-07-26 07:41:39,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:41:39,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:41:39,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:41:39,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:41:39,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:41:39,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:41:39,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:41:39,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:41:39,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:41:39,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 07:41:39,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:41:40,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:41:40,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:41:40,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 07:41:40,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 07:41:40,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:41:40,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 07:41:40,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 07:41:40,Req reqMarketDataType,"(3,) {}"
2025-07-26 07:41:40,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:41:40,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:41:40,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:41:40,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:41:40,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:41:40,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 07:41:40,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 07:41:40,Req reqHistoricalData,"(0, 1668556791952: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:41:41,historicalDataEnd,"reqId: 0, start: ******** 19:41:40 US/Eastern, end: ******** 19:41:40 US/Eastern"
2025-07-26 07:41:41,Req reqHistoricalData,"(6, 1668556793392: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:41:42,historicalDataEnd,"reqId: 6, start: ******** 19:41:41 US/Eastern, end: ******** 19:41:41 US/Eastern"
2025-07-26 07:41:42,Req reqHistoricalData,"(7, 1668556794544: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:41:42,historicalDataEnd,"reqId: 7, start: ******** 08:41:42 Japan, end: ******** 08:41:42 Japan"
2025-07-26 07:41:49,Req reqContractDetails,"(8, 1666480903696: 0,TSM,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:) {}"
2025-07-26 07:41:49,contractDetails,"reqId: 8, contractDetails: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:,TSM,0.01,ACTIVETIM,AD,ADDONT,ADJUST,ALERT,ALGO,ALLOC,AON,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DARKONLY,DARKPOLL,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,DUR,GAT,GTC,GTD,GTT,HID,IBKRATS,ICE,IMB,IOC,LIT,LMT,LOC,MIDPX,MIT,MKT,MOC,MTL,NGCOMB,NODARK,NONALGO,OCA,OPG,OPGREROUT,PEGBENCH,PEGMID,POSTATS,POSTONLY,PREOPGRTH,PRICECHK,REL,REL2MID,RELPCTOFS,RPI,RTH,RTHIGNOPG,SCALE,SCALEODD,SCALERST,SIZECHK,SMARTSTG,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,SWEEP,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,SMART,AMEX,NYSE,CBOE,PHLX,ISE,CHX,ARCA,NASDAQ,DRCTEDGE,BEX,BATS,EDGEA,BYX,IEX,EDGX,FOXRIVER,PEARL,NYSENAT,LTSE,MEMX,IBEOS,OVERNIGHT,TPLUS0,PSX,1,0,TAIWAN SEMICONDUCTOR-SP ADR,,Technology,Semiconductors,Semicon Compo-Intg Circu,US/Eastern,********:0400-********:2000;********:CLOSED;********:CLOSED;********:0400-********:2000;********:0400-********:2000;********:0400-********:2000,********:0930-********:1600;********:CLOSED;********:CLOSED;********:0930-********:1600;********:0930-********:1600;********:0930-********:1600,,0,,,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,1,[*************: ISIN=US8740391003;],,ADR,,,,,,False,False,0,False,,,,,False,,0.0001,0.0001,100,None"
2025-07-26 07:41:49,contractDetailsEnd,reqId: 8
2025-07-26 07:44:41,Req cancelMktData,"(8,) {}"
2025-07-26 07:44:42,error,"reqId: 8, errorCode: 300, errorString: Can't find EId with tickerId:8 contract:"
2025-07-26 07:52:32,nextValidId,orderId: 228
2025-07-26 07:52:32,Req reqAccountUpdates,"('',) {}"
2025-07-26 07:52:33,accountDownloadEnd,accountName: DU7492998
2025-07-26 07:52:33,Req reqMarketDataType,"(1,) {}"
2025-07-26 07:52:33,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:52:33,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:52:33,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:52:33,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:52:33,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:52:33,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:52:33,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:52:33,Req reqMktData,"(5, 1446461060928: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:52:33,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:52:33,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 07:52:34,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:52:34,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 07:52:34,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 07:52:34,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:52:34,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 07:52:34,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:52:34,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 07:52:34,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:52:34,Req reqMarketDataType,"(3,) {}"
2025-07-26 07:52:35,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:52:35,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:52:35,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:52:35,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:52:35,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:52:35,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 07:52:35,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 07:52:35,Req reqHistoricalData,"(0, 1446461169664: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:52:36,historicalDataEnd,"reqId: 0, start: ******** 19:52:35 US/Eastern, end: ******** 19:52:35 US/Eastern"
2025-07-26 07:52:36,Req reqHistoricalData,"(6, 1446461171104: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:52:37,historicalDataEnd,"reqId: 6, start: ******** 19:52:36 US/Eastern, end: ******** 19:52:36 US/Eastern"
2025-07-26 07:52:37,Req reqHistoricalData,"(7, 1446461172256: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:52:37,historicalDataEnd,"reqId: 7, start: ******** 08:52:36 Japan, end: ******** 08:52:36 Japan"
2025-07-26 07:52:41,Req reqContractDetails,"(8, 1446523504784: 0,TSM,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:) {}"
2025-07-26 07:52:41,contractDetails,"reqId: 8, contractDetails: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:,TSM,0.01,ACTIVETIM,AD,ADDONT,ADJUST,ALERT,ALGO,ALLOC,AON,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DARKONLY,DARKPOLL,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,DUR,GAT,GTC,GTD,GTT,HID,IBKRATS,ICE,IMB,IOC,LIT,LMT,LOC,MIDPX,MIT,MKT,MOC,MTL,NGCOMB,NODARK,NONALGO,OCA,OPG,OPGREROUT,PEGBENCH,PEGMID,POSTATS,POSTONLY,PREOPGRTH,PRICECHK,REL,REL2MID,RELPCTOFS,RPI,RTH,RTHIGNOPG,SCALE,SCALEODD,SCALERST,SIZECHK,SMARTSTG,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,SWEEP,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,SMART,AMEX,NYSE,CBOE,PHLX,ISE,CHX,ARCA,NASDAQ,DRCTEDGE,BEX,BATS,EDGEA,BYX,IEX,EDGX,FOXRIVER,PEARL,NYSENAT,LTSE,MEMX,IBEOS,OVERNIGHT,TPLUS0,PSX,1,0,TAIWAN SEMICONDUCTOR-SP ADR,,Technology,Semiconductors,Semicon Compo-Intg Circu,US/Eastern,********:0400-********:2000;********:CLOSED;********:CLOSED;********:0400-********:2000;********:0400-********:2000;********:0400-********:2000,********:0930-********:1600;********:CLOSED;********:CLOSED;********:0930-********:1600;********:0930-********:1600;********:0930-********:1600,,0,,,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,1,[1446523512272: ISIN=US8740391003;],,ADR,,,,,,False,False,0,False,,,,,False,,0.0001,0.0001,100,None"
2025-07-26 07:52:41,contractDetailsEnd,reqId: 8
2025-07-26 07:52:49,Req cancelMktData,"(8,) {}"
2025-07-26 07:53:04,Req reqMarketDataType,"(1,) {}"
2025-07-26 07:53:04,error,"reqId: 8, errorCode: 300, errorString: Can't find EId with tickerId:8 contract:"
2025-07-26 07:53:04,Req reqMktData,"(8, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:53:04,tickReqParams,"reqId: 8,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:53:04,error,"reqId: 8, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:53:04,Req reqMarketDataType,"(3,) {}"
2025-07-26 07:53:05,Req reqMktData,"(8, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:53:05,tickReqParams,"reqId: 8,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:53:05,error,"reqId: 8, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:58:03,nextValidId,orderId: 228
2025-07-26 07:58:03,Req reqAccountUpdates,"('',) {}"
2025-07-26 07:58:04,accountDownloadEnd,accountName: DU7492998
2025-07-26 07:58:04,Req reqMarketDataType,"(1,) {}"
2025-07-26 07:58:04,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:58:04,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:58:04,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:58:04,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:58:04,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:58:04,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:58:04,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:58:04,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:58:04,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:58:04,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:58:04,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 07:58:05,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:58:05,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:58:05,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 07:58:05,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:58:05,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 07:58:05,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 07:58:05,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 07:58:05,Req reqMarketDataType,"(3,) {}"
2025-07-26 07:58:06,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:58:06,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:58:06,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:58:06,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:58:06,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:58:06,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 07:58:06,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 07:58:06,Req reqHistoricalData,"(0, 2716710166288: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:58:07,historicalDataEnd,"reqId: 0, start: ******** 19:58:06 US/Eastern, end: ******** 19:58:06 US/Eastern"
2025-07-26 07:58:07,Req reqHistoricalData,"(6, 2716710167776: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:58:07,historicalDataEnd,"reqId: 6, start: ******** 19:58:06 US/Eastern, end: ******** 19:58:06 US/Eastern"
2025-07-26 07:58:07,Req reqHistoricalData,"(7, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:58:08,historicalDataEnd,"reqId: 7, start: ******** 08:58:07 Japan, end: ******** 08:58:07 Japan"
2025-07-26 07:58:38,Req cancelAccountUpdates,"('',) {}"
2025-07-26 07:58:38,error,"reqId: -1, errorCode: 2100, errorString: API client has been unsubscribed from account data. contract:"
2025-07-26 07:59:07,nextValidId,orderId: 228
2025-07-26 07:59:08,Req reqAccountUpdates,"('',) {}"
2025-07-26 07:59:08,accountDownloadEnd,accountName: DU7492998
2025-07-26 07:59:08,Req reqMarketDataType,"(1,) {}"
2025-07-26 07:59:09,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:59:09,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:59:09,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:59:09,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:59:09,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:59:09,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:59:09,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:59:09,Req reqMktData,"(5, 2400641628448: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:59:09,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:59:09,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:59:09,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 07:59:09,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:59:09,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 07:59:09,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:59:09,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 07:59:09,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 07:59:09,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 07:59:09,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 07:59:09,Req reqMarketDataType,"(3,) {}"
2025-07-26 07:59:10,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:59:10,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 07:59:10,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-26 07:59:10,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-26 07:59:10,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 07:59:10,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 07:59:10,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 07:59:10,Req reqHistoricalData,"(0, 2400641720896: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:59:11,historicalDataEnd,"reqId: 0, start: ******** 19:59:10 US/Eastern, end: ******** 19:59:10 US/Eastern"
2025-07-26 07:59:11,Req reqHistoricalData,"(6, 2400641722432: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:59:12,historicalDataEnd,"reqId: 6, start: ******** 19:59:11 US/Eastern, end: ******** 19:59:11 US/Eastern"
2025-07-26 07:59:12,Req reqHistoricalData,"(7, 2400641723680: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 07:59:12,historicalDataEnd,"reqId: 7, start: ******** 08:59:11 Japan, end: ******** 08:59:11 Japan"
2025-07-26 07:59:14,Req reqContractDetails,"(8, 2398804430048: 0,TSM,STK,,,0,,,SMART,SMART,USD,,,False,,,,combo:) {}"
2025-07-26 07:59:14,contractDetails,"reqId: 8, contractDetails: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:,TSM,0.01,ACTIVETIM,AD,ADDONT,ADJUST,ALERT,ALGO,ALLOC,AON,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DARKONLY,DARKPOLL,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,DUR,GAT,GTC,GTD,GTT,HID,IBKRATS,ICE,IMB,IOC,LIT,LMT,LOC,MIDPX,MIT,MKT,MOC,MTL,NGCOMB,NODARK,NONALGO,OCA,OPG,OPGREROUT,PEGBENCH,PEGMID,POSTATS,POSTONLY,PREOPGRTH,PRICECHK,REL,REL2MID,RELPCTOFS,RPI,RTH,RTHIGNOPG,SCALE,SCALEODD,SCALERST,SIZECHK,SMARTSTG,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,SWEEP,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,SMART,AMEX,NYSE,CBOE,PHLX,ISE,CHX,ARCA,NASDAQ,DRCTEDGE,BEX,BATS,EDGEA,BYX,IEX,EDGX,FOXRIVER,PEARL,NYSENAT,LTSE,MEMX,IBEOS,OVERNIGHT,TPLUS0,PSX,1,0,TAIWAN SEMICONDUCTOR-SP ADR,,Technology,Semiconductors,Semicon Compo-Intg Circu,US/Eastern,********:0400-********:2000;********:CLOSED;********:CLOSED;********:0400-********:2000;********:0400-********:2000;********:0400-********:2000,********:0930-********:1600;********:CLOSED;********:CLOSED;********:0930-********:1600;********:0930-********:1600;********:0930-********:1600,,0,,,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,1,[*************: ISIN=US8740391003;],,ADR,,,,,,False,False,0,False,,,,,False,,0.0001,0.0001,100,None"
2025-07-26 07:59:14,contractDetailsEnd,reqId: 8
2025-07-26 08:07:48,nextValidId,orderId: 228
2025-07-26 08:07:48,Req reqAccountUpdates,"('',) {}"
2025-07-26 08:07:48,accountDownloadEnd,accountName: DU7492998
2025-07-26 08:07:48,Req reqMarketDataType,"(1,) {}"
2025-07-26 08:07:49,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 08:07:49,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 08:07:49,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 08:07:49,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 08:07:49,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 08:07:49,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 08:07:49,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-26 08:07:49,marketDataType,"reqId: 0, marketDataType: REALTIME"
2025-07-26 08:07:49,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-26 08:07:49,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 08:07:49,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 08:07:49,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 08:07:49,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 08:07:49,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 08:07:49,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 08:07:49,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 08:07:50,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 08:07:50,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 08:07:50,Req reqMarketDataType,"(3,) {}"
2025-07-26 08:07:50,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 08:07:50,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 08:07:50,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 08:07:50,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 08:07:50,Req reqHistoricalData,"(6, 2783496789824: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 08:07:51,historicalDataEnd,"reqId: 6, start: ******** 20:07:50 US/Eastern, end: ******** 20:07:50 US/Eastern"
2025-07-26 08:07:51,Req reqHistoricalData,"(7, 2783496791408: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 08:07:52,historicalDataEnd,"reqId: 7, start: ******** 20:07:51 US/Eastern, end: ******** 20:07:51 US/Eastern"
2025-07-26 08:07:52,Req reqHistoricalData,"(8, 2783496792704: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 08:07:53,historicalDataEnd,"reqId: 8, start: ******** 09:07:52 Japan, end: ******** 09:07:52 Japan"
2025-07-26 17:18:07,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:11,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:15,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:19,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:24,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:28,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:32,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:36,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:40,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:44,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:48,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:53,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:18:57,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:19:01,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:19:05,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:19:09,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:19:13,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:19:17,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:19:22,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:19:26,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:19:30,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:19:35,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:19:39,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:19:43,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-26 17:20:00,nextValidId,orderId: 228
2025-07-26 17:20:00,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:hfarm contract:"
2025-07-26 17:20:00,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:jfarm contract:"
2025-07-26 17:20:01,Req reqAccountUpdates,"('',) {}"
2025-07-26 17:20:02,accountDownloadEnd,accountName: DU7492998
2025-07-26 17:20:02,Req reqMarketDataType,"(1,) {}"
2025-07-26 17:20:02,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:20:02,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:20:02,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:20:02,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:20:02,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:20:02,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:20:02,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-26 17:20:02,marketDataType,"reqId: 0, marketDataType: REALTIME"
2025-07-26 17:20:02,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-26 17:20:02,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 17:20:03,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 17:20:03,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:20:03,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 17:20:03,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:20:03,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 17:20:03,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 17:20:03,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:20:03,Req reqMarketDataType,"(3,) {}"
2025-07-26 17:20:04,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:20:04,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 17:20:04,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 17:20:04,Req reqHistoricalData,"(6, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:20:05,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 17:20:06,historicalDataEnd,"reqId: 6, start: ******** 05:20:04 US/Eastern, end: ******** 05:20:04 US/Eastern"
2025-07-26 17:20:06,Req reqHistoricalData,"(7, 2159749685008: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:20:07,historicalDataEnd,"reqId: 7, start: ******** 05:20:06 US/Eastern, end: ******** 05:20:06 US/Eastern"
2025-07-26 17:20:07,Req reqHistoricalData,"(8, 2159749686352: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:20:08,historicalDataEnd,"reqId: 8, start: ******** 18:20:07 Japan, end: ******** 18:20:07 Japan"
2025-07-26 17:20:32,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:hfarm contract:"
2025-07-26 17:21:02,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:jfarm contract:"
2025-07-26 17:23:42,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.hfarm contract:"
2025-07-26 17:23:42,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.hfarm contract:"
2025-07-26 17:23:42,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.usfuture contract:"
2025-07-26 17:23:42,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.eufarmnj contract:"
2025-07-26 17:23:42,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.usfuture contract:"
2025-07-26 17:23:42,error,"reqId: -1, errorCode: 2108, errorString: Market data farm connection is inactive but should be available upon demand.eufarmnj contract:"
2025-07-26 17:33:04,nextValidId,orderId: 228
2025-07-26 17:33:04,Req reqAccountUpdates,"('',) {}"
2025-07-26 17:33:05,accountDownloadEnd,accountName: DU7492998
2025-07-26 17:33:05,Req reqMarketDataType,"(1,) {}"
2025-07-26 17:33:05,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:33:05,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:33:05,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:33:05,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:33:05,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-26 17:33:05,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:33:05,marketDataType,"reqId: 0, marketDataType: REALTIME"
2025-07-26 17:33:05,Req reqMktData,"(5, 1785990479120: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:33:05,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:33:06,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-26 17:33:06,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 17:33:06,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 17:33:06,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:33:06,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 17:33:06,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 17:33:06,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:33:06,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 17:33:06,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 17:33:06,Req reqMarketDataType,"(3,) {}"
2025-07-26 17:33:07,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:33:07,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 17:33:07,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 17:33:07,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 17:33:07,Req reqHistoricalData,"(6, 1785990602896: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:33:07,historicalDataEnd,"reqId: 6, start: ******** 05:33:06 US/Eastern, end: ******** 05:33:06 US/Eastern"
2025-07-26 17:33:07,Req reqHistoricalData,"(7, 1785990604576: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:33:08,historicalDataEnd,"reqId: 7, start: ******** 05:33:07 US/Eastern, end: ******** 05:33:07 US/Eastern"
2025-07-26 17:33:08,Req reqHistoricalData,"(8, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:33:08,historicalDataEnd,"reqId: 8, start: ******** 18:33:07 Japan, end: ******** 18:33:07 Japan"
2025-07-26 17:37:21,Req cancelAccountUpdates,"('',) {}"
2025-07-26 17:37:21,error,"reqId: -1, errorCode: 2100, errorString: API client has been unsubscribed from account data. contract:"
2025-07-26 17:37:35,nextValidId,orderId: 228
2025-07-26 17:37:35,Req reqAccountUpdates,"('',) {}"
2025-07-26 17:37:35,accountDownloadEnd,accountName: DU7492998
2025-07-26 17:37:39,Req reqMarketDataType,"(1,) {}"
2025-07-26 17:37:39,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:37:39,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:37:39,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:37:39,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:37:39,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:37:39,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:37:39,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-26 17:37:39,marketDataType,"reqId: 0, marketDataType: REALTIME"
2025-07-26 17:37:39,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-26 17:37:39,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 17:37:39,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:37:40,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 17:37:40,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 17:37:40,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:37:40,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 17:37:40,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 17:37:40,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:37:40,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 17:37:40,Req reqMarketDataType,"(3,) {}"
2025-07-26 17:37:40,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:37:40,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 17:37:40,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 17:37:40,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 17:37:43,Req reqHistoricalData,"(6, 1683436274896: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:37:44,historicalDataEnd,"reqId: 6, start: ******** 05:37:42 US/Eastern, end: ******** 05:37:42 US/Eastern"
2025-07-26 17:37:44,Req reqHistoricalData,"(7, 1683436276624: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:37:45,historicalDataEnd,"reqId: 7, start: ******** 05:37:43 US/Eastern, end: ******** 05:37:43 US/Eastern"
2025-07-26 17:37:45,Req reqHistoricalData,"(8, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:37:45,historicalDataEnd,"reqId: 8, start: ******** 18:37:44 Japan, end: ******** 18:37:44 Japan"
2025-07-26 17:43:03,nextValidId,orderId: 228
2025-07-26 17:43:03,Req reqAccountUpdates,"('',) {}"
2025-07-26 17:43:03,accountDownloadEnd,accountName: DU7492998
2025-07-26 17:43:06,Req reqMarketDataType,"(1,) {}"
2025-07-26 17:43:06,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:43:06,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:43:06,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:43:06,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:43:06,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:43:06,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:43:06,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-26 17:43:06,marketDataType,"reqId: 0, marketDataType: REALTIME"
2025-07-26 17:43:06,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-26 17:43:06,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 17:43:07,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:43:07,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 17:43:07,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:43:07,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 17:43:07,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 17:43:07,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 17:43:07,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 17:43:07,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 17:43:07,Req reqMarketDataType,"(3,) {}"
2025-07-26 17:43:08,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 17:43:08,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 17:43:08,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 17:43:08,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 17:43:10,Req reqHistoricalData,"(6, 1492276282480: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:43:11,historicalDataEnd,"reqId: 6, start: ******** 05:43:09 US/Eastern, end: ******** 05:43:09 US/Eastern"
2025-07-26 17:43:11,Req reqHistoricalData,"(7, 1492276284256: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:43:11,historicalDataEnd,"reqId: 7, start: ******** 05:43:10 US/Eastern, end: ******** 05:43:10 US/Eastern"
2025-07-26 17:43:11,Req reqHistoricalData,"(8, 1492276285744: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 17:43:12,historicalDataEnd,"reqId: 8, start: ******** 18:43:11 Japan, end: ******** 18:43:11 Japan"
2025-07-26 18:07:04,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:07:05,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:07:08,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:07:19,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:07:45,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:08:36,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:09:38,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:11:00,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:12:06,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:13:11,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:14:18,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:16:13,nextValidId,orderId: 228
2025-07-26 18:16:13,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:16:14,Req reqAccountUpdates,"('',) {}"
2025-07-26 18:16:14,accountDownloadEnd,accountName: DU7492998
2025-07-26 18:16:16,Req reqMarketDataType,"(1,) {}"
2025-07-26 18:16:17,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:16:17,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:16:17,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:16:17,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:16:17,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:16:17,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:16:17,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-26 18:16:17,marketDataType,"reqId: 0, marketDataType: REALTIME"
2025-07-26 18:16:17,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-26 18:16:17,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 18:16:17,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 18:16:17,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 18:16:17,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 18:16:17,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 18:16:17,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 18:16:17,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 18:16:17,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 18:16:17,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 18:16:17,Req reqMarketDataType,"(3,) {}"
2025-07-26 18:16:18,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:16:18,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 18:16:18,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 18:16:18,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 18:16:20,Req reqHistoricalData,"(6, 2960477208112: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 18:16:21,historicalDataEnd,"reqId: 6, start: ******** 06:16:20 US/Eastern, end: ******** 06:16:20 US/Eastern"
2025-07-26 18:16:21,Req reqHistoricalData,"(7, 2959298856336: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 18:16:22,historicalDataEnd,"reqId: 7, start: ******** 06:16:21 US/Eastern, end: ******** 06:16:21 US/Eastern"
2025-07-26 18:16:22,Req reqHistoricalData,"(8, 2959298857872: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 18:16:22,historicalDataEnd,"reqId: 8, start: ******** 19:16:22 Japan, end: ******** 19:16:22 Japan"
2025-07-26 18:16:50,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:18:01,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:18:04,Req cancelAccountUpdates,"('',) {}"
2025-07-26 18:18:04,error,"reqId: -1, errorCode: 2100, errorString: API client has been unsubscribed from account data. contract:"
2025-07-26 18:18:23,nextValidId,orderId: 228
2025-07-26 18:18:23,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:18:23,Req reqAccountUpdates,"('',) {}"
2025-07-26 18:18:23,accountDownloadEnd,accountName: DU7492998
2025-07-26 18:18:26,Req reqMarketDataType,"(1,) {}"
2025-07-26 18:18:27,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:18:27,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:18:27,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:18:27,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:18:27,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:18:27,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:18:27,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-26 18:18:27,marketDataType,"reqId: 0, marketDataType: REALTIME"
2025-07-26 18:18:27,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-26 18:18:27,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-26 18:18:27,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 18:18:27,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 18:18:27,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 18:18:27,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-26 18:18:27,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-26 18:18:27,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-26 18:18:27,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-26 18:18:28,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-26 18:18:28,Req reqMarketDataType,"(3,) {}"
2025-07-26 18:18:28,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-26 18:18:28,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-26 18:18:28,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-26 18:18:28,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-26 18:18:34,Req reqHistoricalData,"(6, 1679164928608: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 18:18:35,historicalDataEnd,"reqId: 6, start: ******** 06:18:34 US/Eastern, end: ******** 06:18:34 US/Eastern"
2025-07-26 18:18:35,Req reqHistoricalData,"(7, 1679164930480: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 18:18:35,historicalDataEnd,"reqId: 7, start: ******** 06:18:34 US/Eastern, end: ******** 06:18:34 US/Eastern"
2025-07-26 18:18:35,Req reqHistoricalData,"(8, 1679164932064: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-26 18:18:36,historicalDataEnd,"reqId: 8, start: ******** 19:18:35 Japan, end: ******** 19:18:35 Japan"
2025-07-26 18:19:19,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:20:41,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:22:02,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:23:14,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:24:28,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:25:47,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:27:08,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:28:23,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:29:37,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:30:54,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:32:10,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:33:26,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:34:38,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:35:49,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:37:10,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:38:20,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:39:31,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:40:34,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:41:58,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:43:06,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:44:14,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:45:27,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:46:34,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:47:41,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:48:54,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:50:03,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:51:17,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:52:30,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:53:43,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:55:02,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:56:21,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:57:33,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:58:42,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 18:59:55,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:01:13,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:02:20,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:03:39,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:04:49,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:06:07,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:07:11,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:08:25,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:09:43,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:10:53,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:12:04,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:13:24,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:14:40,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:15:56,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:17:10,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:18:15,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:19:25,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:20:27,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:21:47,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:23:02,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:24:06,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:25:10,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:26:20,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:27:33,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:28:42,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:29:54,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:31:02,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:32:20,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:33:24,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:34:36,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:35:50,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:37:11,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:38:27,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:39:34,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:40:47,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:42:01,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:43:20,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:44:34,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:45:46,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:47:08,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:48:20,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:49:32,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:50:43,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:51:53,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:53:02,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:54:22,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:55:38,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:56:49,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:57:57,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 19:59:09,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:00:11,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:01:25,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:02:36,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:03:44,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:04:57,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:06:16,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:07:30,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:08:35,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:09:49,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:11:03,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:12:23,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:13:35,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:14:54,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:16:14,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:17:17,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:18:19,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:19:28,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:20:37,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:21:54,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:23:11,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:24:22,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:25:44,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:26:52,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:28:01,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:29:08,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:30:26,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:31:31,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:32:39,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:33:45,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:34:53,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:35:59,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:37:02,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:38:06,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:39:13,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:40:25,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:41:44,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:42:49,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:43:56,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:45:14,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:46:34,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:47:53,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:49:03,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:50:14,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:51:33,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:52:54,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:54:12,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:55:21,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:56:40,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:57:44,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 20:59:03,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:00:16,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:01:34,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:02:39,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:03:59,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:05:08,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:06:12,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:07:34,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:08:38,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:09:41,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:10:49,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:12:04,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:13:17,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:14:38,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:15:51,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:16:56,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:18:03,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:19:06,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:20:10,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:21:21,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:22:08,error,"reqId: -1, errorCode: 2105, errorString: HMDS data farm connection is broken:euhmds contract:"
2025-07-26 21:22:36,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:23:55,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:25:00,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:26:16,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:27:28,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:28:37,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:29:55,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:31:00,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:32:09,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:33:15,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:34:25,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:35:27,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:36:41,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:37:46,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:39:05,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:40:09,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:41:13,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:42:18,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:43:36,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:44:45,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:45:49,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:46:59,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:48:42,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-26 21:48:54,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:usfarm contract:"
2025-07-26 21:48:56,error,"reqId: -1, errorCode: 2157, errorString: Sec-def data farm connection is broken:secdefil contract:"
2025-07-26 21:48:56,error,"reqId: -1, errorCode: 2105, errorString: HMDS data farm connection is broken:ushmds contract:"
2025-07-26 21:48:56,error,"reqId: 6, errorCode: 10182, errorString: Failed to request live updates (disconnected). contract:"
2025-07-26 21:48:56,error,"reqId: 7, errorCode: 10182, errorString: Failed to request live updates (disconnected). contract:"
2025-07-26 21:48:58,error,"reqId: -1, errorCode: 1100, errorString: Connectivity between IBKR and Trader Workstation has been lost. contract:"
2025-07-26 21:49:07,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cafarm contract:"
2025-07-26 21:49:14,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:usfarm contract:"
2025-07-26 21:49:17,error,"reqId: -1, errorCode: 2105, errorString: HMDS data farm connection is broken:ushmds contract:"
2025-07-26 21:49:17,error,"reqId: -1, errorCode: 2157, errorString: Sec-def data farm connection is broken:secdefil contract:"
2025-07-26 21:49:21,error,"reqId: -1, errorCode: 1100, errorString: Connectivity between IBKR and Trader Workstation has been lost. contract:"
2025-07-26 21:49:41,error,"reqId: -1, errorCode: 1100, errorString: Connectivity between IBKR and Trader Workstation has been lost. contract:"
2025-07-26 21:49:42,error,"reqId: -1, errorCode: 1100, errorString: Connectivity between IBKR and Trader Workstation has been lost. contract:"
2025-07-26 21:49:43,error,"reqId: -1, errorCode: 1100, errorString: Connectivity between IBKR and Trader Workstation has been lost. contract:"
2025-07-26 21:49:51,error,"reqId: -1, errorCode: 1100, errorString: Connectivity between IBKR and Trader Workstation has been lost. contract:"
2025-07-26 21:49:58,error,"reqId: -1, errorCode: 1100, errorString: Connectivity between IBKR and Trader Workstation has been lost. contract:"
2025-07-26 21:55:20,error,"reqId: -1, errorCode: 2105, errorString: HMDS data farm connection is broken:euhmds contract:"
2025-07-26 23:21:02,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:jfarm contract:"
2025-07-27 00:10:10,error,"reqId: -1, errorCode: 1100, errorString: Connectivity between IBKR and Trader Workstation has been lost. contract:"
2025-07-27 00:10:12,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:usfarm.nj contract:"
2025-07-27 00:10:12,error,"reqId: -1, errorCode: 2105, errorString: HMDS data farm connection is broken:fundfarm contract:"
2025-07-27 00:10:12,error,"reqId: -1, errorCode: 2105, errorString: HMDS data farm connection is broken:apachmds contract:"
2025-07-27 00:10:12,error,"reqId: -1, errorCode: 2105, errorString: HMDS data farm connection is broken:cashhmds contract:"
2025-07-27 00:10:12,error,"reqId: 8, errorCode: 10182, errorString: Failed to request live updates (disconnected). contract:"
2025-07-27 00:10:13,accountDownloadEnd,accountName: DU7492998
2025-07-27 00:10:16,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-27 00:10:16,error,"reqId: 0, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-27 00:10:16,error,"reqId: -1, errorCode: 1102, errorString: Connectivity between IBKR and Trader Workstation has been restored - data maintained. All data farms are connected: usfarm.nj; jfarm; cafarm; cashfarm; usfarm; apachmds; euhmds; cashhmds; fundfarm; ushmds; secdefil. contract:"
2025-07-27 04:10:37,nextValidId,orderId: 228
2025-07-27 04:10:37,Req reqAccountUpdates,"('',) {}"
2025-07-27 04:10:37,accountDownloadEnd,accountName: DU7492998
2025-07-27 04:10:37,Req reqMarketDataType,"(1,) {}"
2025-07-27 04:10:38,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:10:38,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:10:38,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:10:38,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:10:38,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:10:38,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:10:38,error,"reqId: 0, errorCode: 10168, errorString: Requested market data is not subscribed. Delayed market data is not enabled. contract:"
2025-07-27 04:10:38,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-27 04:10:38,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-27 04:10:38,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 04:10:38,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-27 04:10:38,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-27 04:10:38,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 04:10:38,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-27 04:10:38,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 04:10:39,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-27 04:10:39,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 04:10:39,Req reqMarketDataType,"(3,) {}"
2025-07-27 04:10:39,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:10:39,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:10:39,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-27 04:10:39,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-27 04:10:39,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 04:10:39,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-27 04:10:39,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-27 04:10:39,Req reqHistoricalData,"(6, 1272645029376: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 04:10:41,historicalDataEnd,"reqId: 6, start: ******** 16:10:39 US/Eastern, end: ******** 16:10:39 US/Eastern"
2025-07-27 04:10:41,Req reqHistoricalData,"(7, 1272645031296: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 04:10:42,historicalDataEnd,"reqId: 7, start: ******** 16:10:41 US/Eastern, end: ******** 16:10:41 US/Eastern"
2025-07-27 04:10:42,Req reqHistoricalData,"(8, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 04:10:42,historicalDataEnd,"reqId: 8, start: ******** 05:10:42 Japan, end: ******** 05:10:42 Japan"
2025-07-27 04:13:26,nextValidId,orderId: 228
2025-07-27 04:13:26,Req reqAccountUpdates,"('',) {}"
2025-07-27 04:13:26,accountDownloadEnd,accountName: DU7492998
2025-07-27 04:13:26,Req reqMarketDataType,"(1,) {}"
2025-07-27 04:13:27,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:13:27,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:13:27,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:13:27,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:13:27,error,"reqId: 0, errorCode: 10168, errorString: Requested market data is not subscribed. Delayed market data is not enabled. contract:"
2025-07-27 04:13:27,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:13:27,Req reqMktData,"(5, 3003946626528: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:13:27,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 04:13:27,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-27 04:13:27,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-27 04:13:27,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 04:13:27,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-27 04:13:27,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 04:13:27,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-27 04:13:27,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 04:13:27,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-27 04:13:27,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-27 04:13:27,Req reqMarketDataType,"(3,) {}"
2025-07-27 04:13:28,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:13:28,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:13:28,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-27 04:13:28,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-27 04:13:28,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 04:13:28,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-27 04:13:28,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-27 04:13:28,Req reqHistoricalData,"(6, 3003946784944: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 04:13:29,historicalDataEnd,"reqId: 6, start: ******** 16:13:28 US/Eastern, end: ******** 16:13:28 US/Eastern"
2025-07-27 04:13:29,Req reqHistoricalData,"(7, 3003946786912: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 04:13:30,historicalDataEnd,"reqId: 7, start: ******** 16:13:29 US/Eastern, end: ******** 16:13:29 US/Eastern"
2025-07-27 04:13:30,Req reqHistoricalData,"(8, 3003946788592: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 04:13:30,historicalDataEnd,"reqId: 8, start: ******** 05:13:30 Japan, end: ******** 05:13:30 Japan"
2025-07-27 04:31:47,error,"reqId: -1, errorCode: 2105, errorString: HMDS data farm connection is broken:apachmds contract:"
2025-07-27 04:31:47,error,"reqId: 8, errorCode: 10182, errorString: Failed to request live updates (disconnected). contract:"
2025-07-27 04:59:01,nextValidId,orderId: 228
2025-07-27 04:59:01,Req reqAccountUpdates,"('',) {}"
2025-07-27 04:59:01,accountDownloadEnd,accountName: DU7492998
2025-07-27 04:59:01,Req reqMarketDataType,"(1,) {}"
2025-07-27 04:59:02,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:59:02,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:59:02,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:59:02,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:59:02,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:59:02,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:59:02,error,"reqId: 0, errorCode: 10168, errorString: Requested market data is not subscribed. Delayed market data is not enabled. contract:"
2025-07-27 04:59:02,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-27 04:59:02,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-27 04:59:02,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-27 04:59:02,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 04:59:02,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 04:59:02,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-27 04:59:02,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-27 04:59:02,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 04:59:03,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-27 04:59:03,Req reqMarketDataType,"(3,) {}"
2025-07-27 04:59:03,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 04:59:03,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:59:03,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 04:59:03,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-27 04:59:03,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-27 04:59:03,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 04:59:03,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-27 04:59:03,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-27 04:59:03,Req reqHistoricalData,"(6, 2730487116960: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 04:59:05,historicalDataEnd,"reqId: 6, start: ******** 16:59:03 US/Eastern, end: ******** 16:59:03 US/Eastern"
2025-07-27 04:59:05,Req reqHistoricalData,"(7, 2730487118976: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 04:59:06,historicalDataEnd,"reqId: 7, start: ******** 16:59:05 US/Eastern, end: ******** 16:59:05 US/Eastern"
2025-07-27 04:59:06,Req reqHistoricalData,"(8, 2731433378688: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 04:59:06,historicalDataEnd,"reqId: 8, start: ******** 05:59:06 Japan, end: ******** 05:59:06 Japan"
2025-07-27 05:02:28,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 W', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:02:30,historicalDataEnd,"reqId: 9, start: 20250719 17:02:28 US/Eastern, end: ******** 17:02:28 US/Eastern"
2025-07-27 05:02:36,Req reqHistoricalData,"() {'reqId': 10, 'contract': *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:02:37,historicalDataEnd,"reqId: 10, start: ******** 17:02:36 US/Eastern, end: ******** 17:02:36 US/Eastern"
2025-07-27 05:03:04,Req cancelHistoricalData,"(10,) {}"
2025-07-27 05:03:52,error,"reqId: 10, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 10 contract:"
2025-07-27 05:03:54,Req reqHistoricalData,"() {'reqId': 11, 'contract': *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:03:54,historicalDataEnd,"reqId: 11, start: ******** 06:03:53 Japan, end: ******** 06:03:53 Japan"
2025-07-27 05:03:55,Req cancelHistoricalData,"(9,) {}"
2025-07-27 05:04:45,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-27 05:16:02,nextValidId,orderId: 228
2025-07-27 05:16:37,Req reqAccountUpdates,"('',) {}"
2025-07-27 05:16:37,accountDownloadEnd,accountName: DU7492998
2025-07-27 05:16:38,Req reqMarketDataType,"(1,) {}"
2025-07-27 05:16:40,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:16:40,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:16:40,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:16:40,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:16:40,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:16:40,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:16:40,error,"reqId: 0, errorCode: 10168, errorString: Requested market data is not subscribed. Delayed market data is not enabled. contract:"
2025-07-27 05:16:43,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-27 05:16:43,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-27 05:16:43,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 05:16:43,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-27 05:16:43,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-27 05:16:43,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:16:43,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-27 05:16:43,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:16:43,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-27 05:16:43,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:18:54,nextValidId,orderId: 228
2025-07-27 05:18:58,Req reqAccountUpdates,"('',) {}"
2025-07-27 05:18:58,accountDownloadEnd,accountName: DU7492998
2025-07-27 05:18:59,Req reqMarketDataType,"(1,) {}"
2025-07-27 05:19:00,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:19:00,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:19:00,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:19:00,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:19:00,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:19:00,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:19:01,error,"reqId: 0, errorCode: 10168, errorString: Requested market data is not subscribed. Delayed market data is not enabled. contract:"
2025-07-27 05:19:01,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:19:02,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-27 05:19:02,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-27 05:19:03,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-27 05:19:03,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:19:05,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 05:19:05,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-27 05:19:05,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-27 05:19:06,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:19:08,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-27 05:20:08,nextValidId,orderId: 228
2025-07-27 05:20:11,Req reqAccountUpdates,"('',) {}"
2025-07-27 05:20:11,accountDownloadEnd,accountName: DU7492998
2025-07-27 05:20:13,Req reqMarketDataType,"(1,) {}"
2025-07-27 05:20:14,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:20:14,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:20:14,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:20:14,error,"reqId: 0, errorCode: 10168, errorString: Requested market data is not subscribed. Delayed market data is not enabled. contract:"
2025-07-27 05:20:14,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:20:14,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:20:14,Req reqMktData,"(5, 2723649145696: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:20:15,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:20:15,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-27 05:20:15,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-27 05:20:16,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:20:16,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-27 05:20:16,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:20:18,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 05:20:18,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-27 05:20:18,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-27 05:20:22,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-27 05:20:25,Req reqMarketDataType,"(3,) {}"
2025-07-27 05:20:30,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:20:30,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:20:30,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-27 05:20:30,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-27 05:20:35,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 05:20:35,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-27 05:20:35,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-27 05:20:50,Req reqHistoricalData,"(6, 2723649156400: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 05:20:52,historicalDataEnd,"reqId: 6, start: ******** 17:20:50 US/Eastern, end: ******** 17:20:50 US/Eastern"
2025-07-27 05:21:44,Req reqHistoricalData,"() {'reqId': 7, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:21:44,Req reqHistoricalData,"(8, 2723649158464: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 05:21:45,historicalDataEnd,"reqId: 8, start: ******** 17:21:44 US/Eastern, end: ******** 17:21:44 US/Eastern"
2025-07-27 05:22:24,Req reqHistoricalData,"(9, 2723717530736: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 05:22:24,historicalDataEnd,"reqId: 7, start: ******** 17:21:44 US/Eastern, end: ******** 17:21:44 US/Eastern"
2025-07-27 05:22:24,historicalDataEnd,"reqId: 9, start: ******** 06:22:23 Japan, end: ******** 06:22:23 Japan"
2025-07-27 05:22:33,Req cancelHistoricalData,"(7,) {}"
2025-07-27 05:23:06,error,"reqId: 7, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 7 contract:"
2025-07-27 05:25:08,nextValidId,orderId: 228
2025-07-27 05:25:17,Req reqAccountUpdates,"('',) {}"
2025-07-27 05:25:17,accountDownloadEnd,accountName: DU7492998
2025-07-27 05:25:18,Req reqMarketDataType,"(1,) {}"
2025-07-27 05:25:18,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:25:18,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:25:18,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:25:18,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:25:18,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:25:18,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:25:18,error,"reqId: 0, errorCode: 10168, errorString: Requested market data is not subscribed. Delayed market data is not enabled. contract:"
2025-07-27 05:25:18,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:25:18,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-27 05:25:18,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-27 05:25:18,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-27 05:25:18,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:25:19,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-27 05:25:19,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:25:19,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 05:25:19,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-27 05:25:19,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-27 05:25:19,Req reqMarketDataType,"(3,) {}"
2025-07-27 05:25:19,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:25:19,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:25:19,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-27 05:25:19,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-27 05:25:19,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 05:25:19,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-27 05:25:19,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-27 05:25:20,Req reqHistoricalData,"(6, 2199551019776: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 05:25:20,historicalDataEnd,"reqId: 6, start: ******** 17:25:19 US/Eastern, end: ******** 17:25:19 US/Eastern"
2025-07-27 05:25:20,Req reqHistoricalData,"(7, 2199551021888: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 05:25:21,historicalDataEnd,"reqId: 7, start: ******** 17:25:20 US/Eastern, end: ******** 17:25:20 US/Eastern"
2025-07-27 05:25:21,Req reqHistoricalData,"(8, 2199551023712: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 05:25:22,historicalDataEnd,"reqId: 8, start: ******** 06:25:21 Japan, end: ******** 06:25:21 Japan"
2025-07-27 05:25:55,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:25:57,historicalDataEnd,"reqId: 9, start: ******** 17:25:55 US/Eastern, end: ******** 17:25:55 US/Eastern"
2025-07-27 05:26:45,Req cancelHistoricalData,"(9,) {}"
2025-07-27 05:26:54,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-27 05:34:06,nextValidId,orderId: 228
2025-07-27 05:34:11,Req reqAccountUpdates,"('',) {}"
2025-07-27 05:34:11,accountDownloadEnd,accountName: DU7492998
2025-07-27 05:34:15,Req reqMarketDataType,"(1,) {}"
2025-07-27 05:34:15,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:34:15,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:34:15,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:34:15,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:34:15,error,"reqId: 0, errorCode: 10168, errorString: Requested market data is not subscribed. Delayed market data is not enabled. contract:"
2025-07-27 05:34:15,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:34:15,Req reqMktData,"(5, 2251379842256: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:34:15,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 0"
2025-07-27 05:34:15,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-27 05:34:16,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-27 05:34:16,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:34:16,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 05:34:16,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-27 05:34:16,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-27 05:34:16,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:34:16,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-27 05:34:16,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-27 05:34:16,Req reqMarketDataType,"(3,) {}"
2025-07-27 05:34:16,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:34:16,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-27 05:34:16,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-27 05:34:16,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 0"
2025-07-27 05:34:17,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-27 05:34:17,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-27 05:34:17,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-27 05:34:17,Req reqHistoricalData,"(6, 2251379968000: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 05:34:17,historicalDataEnd,"reqId: 6, start: ******** 17:34:16 US/Eastern, end: ******** 17:34:16 US/Eastern"
2025-07-27 05:34:17,Req reqHistoricalData,"(7, 2251379970160: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 05:34:18,historicalDataEnd,"reqId: 7, start: ******** 17:34:17 US/Eastern, end: ******** 17:34:17 US/Eastern"
2025-07-27 05:34:18,Req reqHistoricalData,"(8, 2251379972032: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-27 05:34:18,historicalDataEnd,"reqId: 8, start: ******** 06:34:18 Japan, end: ******** 06:34:18 Japan"
2025-07-27 05:34:21,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:34:22,historicalDataEnd,"reqId: 9, start: ******** 17:34:21 US/Eastern, end: ******** 17:34:21 US/Eastern"
2025-07-27 05:34:35,Req cancelHistoricalData,"(9,) {}"
2025-07-27 05:34:41,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-27 05:34:43,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:34:44,historicalDataEnd,"reqId: 9, start: ******** 06:34:42 Japan, end: ******** 06:34:42 Japan"
2025-07-27 05:34:56,Req cancelHistoricalData,"(9,) {}"
2025-07-27 05:35:05,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-27 05:35:07,Req reqHistoricalData,"() {'reqId': 9, 'contract': 2251379842256: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:35:08,historicalDataEnd,"reqId: 9, start: ******** 17:35:07 US/Eastern, end: ******** 17:35:07 US/Eastern"
2025-07-27 05:35:26,Req cancelHistoricalData,"(9,) {}"
2025-07-27 05:35:50,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-27 05:35:52,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:35:53,historicalDataEnd,"reqId: 9, start: ******** 06:35:52 Japan, end: ******** 06:35:52 Japan"
2025-07-27 05:35:59,Req cancelHistoricalData,"(9,) {}"
2025-07-27 05:36:22,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-27 05:36:24,Req reqHistoricalData,"() {'reqId': 9, 'contract': 2251379842256: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:36:25,historicalDataEnd,"reqId: 9, start: ******** 17:36:24 US/Eastern, end: ******** 17:36:24 US/Eastern"
2025-07-27 05:36:29,Req reqHistoricalData,"() {'reqId': 10, 'contract': 2251379842256: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:36:30,historicalDataEnd,"reqId: 10, start: ******** 17:31:29 US/Eastern, end: ******** 17:36:29 US/Eastern"
2025-07-27 05:36:33,Req cancelHistoricalData,"(9,) {}"
2025-07-27 05:36:41,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-27 05:36:43,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:36:44,historicalDataEnd,"reqId: 9, start: ******** 06:36:43 Japan, end: ******** 06:36:43 Japan"
2025-07-27 05:36:44,Req cancelHistoricalData,"(10,) {}"
2025-07-27 05:36:54,error,"reqId: 10, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 10 contract:"
2025-07-27 05:36:56,Req reqHistoricalData,"() {'reqId': 10, 'contract': *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:36:57,historicalDataEnd,"reqId: 10, start: ******** 06:31:56 Japan, end: ******** 06:36:56 Japan"
2025-07-27 05:37:06,Req cancelHistoricalData,"(9,) {}"
2025-07-27 05:37:16,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-27 05:37:17,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:37:19,historicalDataEnd,"reqId: 9, start: ******** 17:37:17 US/Eastern, end: ******** 17:37:17 US/Eastern"
2025-07-27 05:37:19,Req cancelHistoricalData,"(10,) {}"
2025-07-27 05:37:26,error,"reqId: 10, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 10 contract:"
2025-07-27 05:37:28,Req reqHistoricalData,"() {'reqId': 10, 'contract': *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:37:29,historicalDataEnd,"reqId: 10, start: ******** 17:32:28 US/Eastern, end: ******** 17:37:28 US/Eastern"
2025-07-27 05:37:33,Req cancelHistoricalData,"(9,) {}"
2025-07-27 05:37:41,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-27 05:37:43,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:37:44,historicalDataEnd,"reqId: 9, start: ******** 17:37:43 US/Eastern, end: ******** 17:37:43 US/Eastern"
2025-07-27 05:37:44,Req cancelHistoricalData,"(10,) {}"
2025-07-27 05:37:47,error,"reqId: 10, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 10 contract:"
2025-07-27 05:37:49,Req reqHistoricalData,"() {'reqId': 10, 'contract': *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:37:50,historicalDataEnd,"reqId: 10, start: ******** 17:32:49 US/Eastern, end: ******** 17:37:49 US/Eastern"
2025-07-27 05:37:52,Req cancelHistoricalData,"(9,) {}"
2025-07-27 05:38:00,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-27 05:38:02,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:38:03,historicalDataEnd,"reqId: 9, start: ******** 17:38:02 US/Eastern, end: ******** 17:38:02 US/Eastern"
2025-07-27 05:38:03,Req cancelHistoricalData,"(10,) {}"
2025-07-27 05:41:58,error,"reqId: 10, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 10 contract:"
2025-07-27 05:42:00,Req reqHistoricalData,"() {'reqId': 10, 'contract': *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:42:00,historicalDataEnd,"reqId: 10, start: ******** 17:36:59 US/Eastern, end: ******** 17:41:59 US/Eastern"
2025-07-27 05:42:04,Req cancelHistoricalData,"(7,) {}"
2025-07-27 05:42:17,error,"reqId: 7, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 7 contract:"
2025-07-27 05:42:24,Req cancelHistoricalData,"(9,) {}"
2025-07-27 05:43:06,error,"reqId: 9, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 9 contract:"
2025-07-27 05:43:08,Req reqHistoricalData,"() {'reqId': 7, 'contract': *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '3 Y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:43:10,historicalDataEnd,"reqId: 7, start: ******** 17:43:07 US/Eastern, end: ******** 17:43:07 US/Eastern"
2025-07-27 05:43:10,Req cancelHistoricalData,"(10,) {}"
2025-07-27 05:43:14,error,"reqId: 10, errorCode: 162, errorString: Historical Market Data Service error message:API historical data query cancelled: 10 contract:"
2025-07-27 05:43:16,Req reqHistoricalData,"() {'reqId': 9, 'contract': *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, 'endDateTime': '', 'durationStr': '300 S', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': True, 'chartOptions': []}"
2025-07-27 05:43:16,historicalDataEnd,"reqId: 9, start: ******** 17:38:15 US/Eastern, end: ******** 17:43:15 US/Eastern"
2025-07-27 05:43:26,Req cancelAccountUpdates,"('',) {}"
2025-07-27 05:43:26,error,"reqId: -1, errorCode: 2100, errorString: API client has been unsubscribed from account data. contract:"
2025-07-28 02:43:40,nextValidId,orderId: 228
2025-07-28 02:43:40,Req reqAccountUpdates,"('',) {}"
2025-07-28 02:43:41,accountDownloadEnd,accountName: DU7492998
2025-07-28 02:43:41,Req reqMarketDataType,"(1,) {}"
2025-07-28 02:43:41,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-28 02:43:41,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-28 02:43:41,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-28 02:43:41,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-28 02:43:41,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-28 02:43:41,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-28 02:43:42,tickReqParams,"reqId: 0,  minTick: 0.0, bboExchange: a60001, snapshotPermissions: 4"
2025-07-28 02:43:42,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.TSM NYSE/TOP/ALL contract:"
2025-07-28 02:43:42,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-28 02:43:42,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-28 02:43:42,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-28 02:43:42,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-28 02:43:42,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-28 02:43:42,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-28 02:43:42,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-28 02:43:42,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-28 02:43:42,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-28 02:43:42,Req reqMarketDataType,"(3,) {}"
2025-07-28 02:43:43,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-28 02:43:43,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-28 02:43:43,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-28 02:43:43,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-28 02:43:43,tickReqParams,"reqId: 0,  minTick: 0.0, bboExchange: a60001, snapshotPermissions: 4"
2025-07-28 02:43:43,marketDataType,"reqId: 0, marketDataType: DELAYED"
2025-07-28 02:43:43,error,"reqId: 0, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-28 02:43:43,Req reqHistoricalData,"(6, 1260358657296: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-28 02:43:43,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-28 02:43:46,historicalDataEnd,"reqId: 6, start: ******** 14:43:42 US/Eastern, end: ******** 14:43:42 US/Eastern"
2025-07-28 02:43:46,Req reqHistoricalData,"(7, 1260358659312: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-28 02:43:47,historicalDataEnd,"reqId: 7, start: ******** 14:43:45 US/Eastern, end: ******** 14:43:45 US/Eastern"
2025-07-28 02:43:47,Req reqHistoricalData,"(8, 1260358661232: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-28 02:43:48,historicalDataEnd,"reqId: 8, start: 20240728 03:43:46 Japan, end: ******** 03:43:46 Japan"
2025-07-28 04:32:46,error,"reqId: -1, errorCode: 2105, errorString: HMDS data farm connection is broken:apachmds contract:"
2025-07-28 04:32:46,error,"reqId: 8, errorCode: 10182, errorString: Failed to request live updates (disconnected). contract:"
2025-07-28 05:04:04,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-28 05:04:05,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-28 05:04:10,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-28 05:04:22,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-28 05:04:44,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-28 05:05:31,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-28 05:06:59,error,"reqId: -1, errorCode: 2103, errorString: Market data farm connection is broken:cashfarm contract:"
2025-07-29 03:15:15,nextValidId,orderId: 228
2025-07-29 03:15:15,Req reqAccountUpdates,"('',) {}"
2025-07-29 03:15:15,accountDownloadEnd,accountName: DU7492998
2025-07-29 03:15:16,Req reqMarketDataType,"(1,) {}"
2025-07-29 03:15:16,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-29 03:15:16,Req reqMktData,"(1, *************: *********,PLTR,STK,,,0,,,NASDAQ,NASDAQ,USD,PLTR,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-29 03:15:16,Req reqMktData,"(2, *************: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-29 03:15:16,Req reqMktData,"(3, *************: *********,TEM,STK,,,0,,,NASDAQ,NASDAQ,USD,TEM,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-29 03:15:16,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-29 03:15:16,Req reqMktData,"(5, *************: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', False, False, []) {}"
2025-07-29 03:15:16,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-29 03:15:16,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-29 03:15:16,tickReqParams,"reqId: 2,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-29 03:15:16,marketDataType,"reqId: 2, marketDataType: REALTIME"
2025-07-29 03:15:16,error,"reqId: 4, errorCode: 354, errorString: Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.9697 TSEJ/TOP/ALL contract:"
2025-07-29 03:15:17,marketDataType,"reqId: 1, marketDataType: REALTIME"
2025-07-29 03:15:17,tickReqParams,"reqId: 1,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-29 03:15:17,marketDataType,"reqId: 3, marketDataType: REALTIME"
2025-07-29 03:15:17,tickReqParams,"reqId: 3,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-29 03:15:17,marketDataType,"reqId: 5, marketDataType: REALTIME"
2025-07-29 03:15:17,tickReqParams,"reqId: 5,  minTick: 0.01, bboExchange: 9c0001, snapshotPermissions: 3"
2025-07-29 03:15:17,Req reqMarketDataType,"(3,) {}"
2025-07-29 03:15:18,Req reqMktData,"(0, *************: 6223250,TSM,STK,,,0,,,SMART,NYSE,USD,TSM,TSM,False,,,,combo:, '', False, False, []) {}"
2025-07-29 03:15:18,Req reqMktData,"(4, *************: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', False, False, []) {}"
2025-07-29 03:15:18,tickReqParams,"reqId: 0,  minTick: 0.01, bboExchange: a60001, snapshotPermissions: 4"
2025-07-29 03:15:18,error,"reqId: 0, errorCode: 10089, errorString: Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.TSM NYSE/TOP/ALL contract:"
2025-07-29 03:15:18,marketDataType,"reqId: 4, marketDataType: DELAYED"
2025-07-29 03:15:18,error,"reqId: 4, errorCode: 10167, errorString: Requested market data is not subscribed. Displaying delayed market data. contract:"
2025-07-29 03:15:18,Req reqHistoricalData,"(0, 2746941336768: ********,TSLA,STK,,,0,,,NASDAQ,NASDAQ,USD,TSLA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-29 03:15:18,tickReqParams,"reqId: 4,  minTick: 0.1, bboExchange: , snapshotPermissions: 0"
2025-07-29 03:15:19,historicalDataEnd,"reqId: 0, start: 20240728 15:15:17 US/Eastern, end: ******** 15:15:17 US/Eastern"
2025-07-29 03:15:19,Req reqHistoricalData,"(6, 2746941339024: 4815747,NVDA,STK,,,0,,,SMART,NASDAQ,USD,NVDA,NMS,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-29 03:15:20,historicalDataEnd,"reqId: 6, start: 20240728 15:15:18 US/Eastern, end: ******** 15:15:18 US/Eastern"
2025-07-29 03:15:20,Req reqHistoricalData,"(7, 2746941340992: ********,9697,STK,,,0,,,SMART,TSEJ,JPY,9697.T,9697,False,,,,combo:, '', '1 Y', '1 day', 'TRADES', 0, 1, True, []) {}"
2025-07-29 03:15:21,historicalDataEnd,"reqId: 7, start: 20240729 04:15:19 Japan, end: ******** 04:15:19 Japan"
2025-07-29 05:57:24,nextValidId,orderId: 228
2025-07-29 05:59:01,nextValidId,orderId: 228
2025-07-29 05:59:31,nextValidId,orderId: 228
2025-07-29 05:59:32,Req reqHistoricalData,"() {'reqId': 1, 'contract': 3069736450144: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 D', 'barSizeSetting': '1 hour', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-29 05:59:51,nextValidId,orderId: 228
2025-07-29 05:59:51,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2015113964832: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 D', 'barSizeSetting': '1 hour', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-29 05:59:52,historicalDataEnd,"reqId: 1, start: ******** 17:59:51 US/Eastern, end: ******** 17:59:51 US/Eastern"
2025-07-29 06:01:03,nextValidId,orderId: 228
2025-07-29 06:01:03,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2706876217200: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 D', 'barSizeSetting': '1 hour', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-29 06:01:03,historicalDataEnd,"reqId: 1, start: ******** 18:01:03 US/Eastern, end: ******** 18:01:03 US/Eastern"
2025-07-29 06:01:22,nextValidId,orderId: 228
2025-07-29 06:01:22,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2457924748208: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 D', 'barSizeSetting': '1 hour', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-29 06:01:23,historicalDataEnd,"reqId: 1, start: ******** 18:01:22 US/Eastern, end: ******** 18:01:22 US/Eastern"
2025-07-29 06:28:24,nextValidId,orderId: 228
2025-07-29 06:28:24,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2046281540160: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 D', 'barSizeSetting': '1 hour', 'whatToShow': 'TRADES', 'useRTH': 0, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-29 06:28:24,historicalDataEnd,"reqId: 1, start: ******** 18:28:24 US/Eastern, end: ******** 18:28:24 US/Eastern"
2025-07-29 06:28:45,nextValidId,orderId: 228
2025-07-29 06:28:45,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2628031559056: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 D', 'barSizeSetting': '1 hour', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-29 06:28:46,historicalDataEnd,"reqId: 1, start: ******** 18:28:46 US/Eastern, end: ******** 18:28:46 US/Eastern"
2025-07-30 05:09:49,nextValidId,orderId: 228
2025-07-30 05:09:49,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2431300447472: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 D', 'barSizeSetting': '1 hour', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-30 05:09:49,historicalDataEnd,"reqId: 1, start: ******** 17:09:48 US/Eastern, end: ******** 17:09:48 US/Eastern"
2025-07-30 05:15:56,nextValidId,orderId: 228
2025-07-30 05:15:56,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2001947137792: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 D', 'barSizeSetting': '1 hour', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-30 05:15:56,historicalDataEnd,"reqId: 1, start: ******** 17:15:56 US/Eastern, end: ******** 17:15:56 US/Eastern"
2025-07-31 02:17:07,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-31 02:17:11,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-31 02:17:15,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-31 02:17:15,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-31 02:17:19,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-31 02:17:19,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-07-31 02:19:08,nextValidId,orderId: 228
2025-07-31 02:19:08,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2883568350272: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:19:08,historicalDataEnd,"reqId: 1, start: ******** 14:19:07 US/Eastern, end: ******** 14:19:07 US/Eastern"
2025-07-31 02:22:22,nextValidId,orderId: 228
2025-07-31 02:22:22,Req reqHistoricalData,"() {'reqId': 1, 'contract': 1805210575712: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '2005-07-28', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:22:22,error,"reqId: 1, errorCode: 10314, errorString: End Date/Time: The date, time, or time-zone entered is invalid. The correct format is yyyymmdd hh:mm:ss xx/xxxx where yyyymmdd and xx/xxxx are optional. E.g.: 20031126 15:59:00 US/Eastern  Note that there is a space between the date and time, and between the time and time-zone.  If no date is specified, current date is assumed. If no time-zone is specified, local time-zone is assumed(deprecated).  You can also provide yyyymmddd-hh:mm:ss time is in UTC. Note that there is a dash between the date and time in UTC notation. contract:"
2025-07-31 02:22:38,nextValidId,orderId: 228
2025-07-31 02:22:38,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2903312263904: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '2005-07-28', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:22:38,error,"reqId: 1, errorCode: 10314, errorString: End Date/Time: The date, time, or time-zone entered is invalid. The correct format is yyyymmdd hh:mm:ss xx/xxxx where yyyymmdd and xx/xxxx are optional. E.g.: 20031126 15:59:00 US/Eastern  Note that there is a space between the date and time, and between the time and time-zone.  If no date is specified, current date is assumed. If no time-zone is specified, local time-zone is assumed(deprecated).  You can also provide yyyymmddd-hh:mm:ss time is in UTC. Note that there is a dash between the date and time in UTC notation. contract:"
2025-07-31 02:26:40,nextValidId,orderId: 228
2025-07-31 02:26:40,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2415758632160: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '20050728 16:00:00', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:26:40,error,"reqId: 1, errorCode: 2174, errorString: Warning: You submitted request with date-time attributes without explicit time zone. Please switch to use yyyymmdd-hh:mm:ss in UTC or use instrument time zone, like US/Eastern. Implied time zone functionality will be removed in the next API release contract:"
2025-07-31 02:29:00,nextValidId,orderId: 228
2025-07-31 02:29:00,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2765174222976: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '20050728 16:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:29:01,error,"reqId: 1, errorCode: 162, errorString: Historical Market Data Service error message:HMDS query returned no data: TSLA@SMART Trades contract:"
2025-07-31 02:30:06,nextValidId,orderId: 228
2025-07-31 02:30:06,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2407064797744: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '20050728 00:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:30:07,error,"reqId: 1, errorCode: 162, errorString: Historical Market Data Service error message:HMDS query returned no data: TSLA@SMART Trades contract:"
2025-07-31 02:31:11,nextValidId,orderId: 228
2025-07-31 02:31:11,Req reqHistoricalData,"() {'reqId': 1, 'contract': 1886726699392: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '20050728 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:31:11,error,"reqId: 1, errorCode: 10314, errorString: End Date/Time: The date, time, or time-zone entered is invalid. The correct format is yyyymmdd hh:mm:ss xx/xxxx where yyyymmdd and xx/xxxx are optional. E.g.: 20031126 15:59:00 US/Eastern  Note that there is a space between the date and time, and between the time and time-zone.  If no date is specified, current date is assumed. If no time-zone is specified, local time-zone is assumed(deprecated).  You can also provide yyyymmddd-hh:mm:ss time is in UTC. Note that there is a dash between the date and time in UTC notation. contract:"
2025-07-31 02:32:02,nextValidId,orderId: 228
2025-07-31 02:32:02,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2025995474832: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '20050728', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:32:02,error,"reqId: 1, errorCode: 10314, errorString: End Date/Time: The date, time, or time-zone entered is invalid. The correct format is yyyymmdd hh:mm:ss xx/xxxx where yyyymmdd and xx/xxxx are optional. E.g.: 20031126 15:59:00 US/Eastern  Note that there is a space between the date and time, and between the time and time-zone.  If no date is specified, current date is assumed. If no time-zone is specified, local time-zone is assumed(deprecated).  You can also provide yyyymmddd-hh:mm:ss time is in UTC. Note that there is a dash between the date and time in UTC notation. contract:"
2025-07-31 02:32:53,nextValidId,orderId: 228
2025-07-31 02:32:53,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2417623431168: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '20050729 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:32:54,error,"reqId: 1, errorCode: 162, errorString: Historical Market Data Service error message:HMDS query returned no data: TSLA@SMART Trades contract:"
2025-07-31 02:36:02,nextValidId,orderId: 228
2025-07-31 02:36:02,Req reqHistoricalData,"() {'reqId': 1, 'contract': 1584514872624: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:36:02,historicalDataEnd,"reqId: 1, start: ******** 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
2025-07-31 02:37:47,nextValidId,orderId: 228
2025-07-31 02:37:47,Req reqHistoricalData,"() {'reqId': 1, 'contract': 1537339647440: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:37:47,historicalDataEnd,"reqId: 1, start: ******** 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
2025-07-31 02:44:37,nextValidId,orderId: 228
2025-07-31 02:44:37,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2567794629168: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '1 hour', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-07-31 02:44:38,historicalDataEnd,"reqId: 1, start: ******** 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
2025-08-01 00:28:24,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:28:29,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:28:33,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:28:37,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:28:41,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:28:45,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:28:49,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:28:53,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:28:58,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:29:22,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:30:26,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:30:30,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:30:34,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:30:38,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:30:42,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:30:46,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:30:51,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:30:55,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:30:59,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:03,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:07,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:11,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:15,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:20,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:24,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:28,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:32,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:36,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:40,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:44,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:49,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:53,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:31:57,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:02,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:06,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:10,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:14,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:18,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:22,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:26,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:31,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:35,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:40,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:44,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:48,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:52,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:32:56,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:33:00,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:33:04,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:33:09,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:33:13,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:33:17,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:33:22,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:33:26,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:33:30,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:33:34,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:33:38,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-01 00:41:09,nextValidId,orderId: 228
2025-08-01 00:41:09,Req reqHistoricalData,"() {'reqId': 1, 'contract': 1880701973760: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-01 00:41:10,historicalDataEnd,"reqId: 1, start: ******** 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
2025-08-01 01:49:18,nextValidId,orderId: 228
2025-08-01 01:49:18,Req reqHistoricalData,"() {'reqId': 1, 'contract': 1921621771088: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-01 01:49:18,historicalDataEnd,"reqId: 1, start: ******** 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
2025-08-01 03:00:44,nextValidId,orderId: 228
2025-08-01 03:00:45,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2141580886144: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '5 secs', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-01 03:00:58,historicalDataEnd,"reqId: 1, start: ******** 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
2025-08-01 03:04:29,nextValidId,orderId: 228
2025-08-01 03:04:29,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2646709037648: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '30 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-01 03:04:30,historicalDataEnd,"reqId: 1, start: ******** 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
2025-08-02 06:26:39,nextValidId,orderId: 228
2025-08-02 06:26:39,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2708134795104: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '31 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-02 06:26:39,error,"reqId: 1, errorCode: 321, errorString: Error validating request.-'bM' : cause - Historical data bar size setting is invalid. Legal ones are: 1 secs, 5 secs, 10 secs, 15 secs, 30 secs, 1 min, 2 mins, 3 mins, 5 mins, 10 mins, 15 mins, 20 mins, 30 mins, 1 hour, 2 hours, 3 hours, 4 hours, 8 hours, 1 day, 1W, 1M contract:"
2025-08-02 06:27:54,nextValidId,orderId: 228
2025-08-02 06:27:54,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2410286809552: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '31 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-02 06:27:54,error,"reqId: 1, errorCode: 321, errorString: Error validating request.-'bM' : cause - Historical data bar size setting is invalid. Legal ones are: 1 secs, 5 secs, 10 secs, 15 secs, 30 secs, 1 min, 2 mins, 3 mins, 5 mins, 10 mins, 15 mins, 20 mins, 30 mins, 1 hour, 2 hours, 3 hours, 4 hours, 8 hours, 1 day, 1W, 1M contract:"
2025-08-05 05:59:53,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-05 05:59:57,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-05 06:00:02,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-05 06:00:06,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-05 06:00:10,error,"reqId: -1, errorCode: 502, errorString: Couldn't connect to TWS. Confirm that ""Enable ActiveX and Socket EClients"" 
is enabled and connection port is the same as ""Socket Port"" on the 
TWS ""Edit->Global Configuration...->API->Settings"" menu. Live Trading ports: 
TWS: 7496; IB Gateway: 4001. Simulated Trading ports for new installations 
of version 954.1 or newer:  TWS: 7497; IB Gateway: 4002 contract:None"
2025-08-05 06:01:42,nextValidId,orderId: 228
2025-08-05 06:01:42,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2460877161536: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '31 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-05 06:01:42,error,"reqId: 1, errorCode: 321, errorString: Error validating request.-'bM' : cause - Historical data bar size setting is invalid. Legal ones are: 1 secs, 5 secs, 10 secs, 15 secs, 30 secs, 1 min, 2 mins, 3 mins, 5 mins, 10 mins, 15 mins, 20 mins, 30 mins, 1 hour, 2 hours, 3 hours, 4 hours, 8 hours, 1 day, 1W, 1M contract:"
2025-08-05 06:07:11,nextValidId,orderId: 228
2025-08-05 06:07:11,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2132452190656: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '31 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-05 06:07:11,error,"reqId: 1, errorCode: 321, errorString: Error validating request.-'bM' : cause - Historical data bar size setting is invalid. Legal ones are: 1 secs, 5 secs, 10 secs, 15 secs, 30 secs, 1 min, 2 mins, 3 mins, 5 mins, 10 mins, 15 mins, 20 mins, 30 mins, 1 hour, 2 hours, 3 hours, 4 hours, 8 hours, 1 day, 1W, 1M contract:"
2025-08-05 06:07:48,nextValidId,orderId: 228
2025-08-05 06:07:48,Req reqHistoricalData,"() {'reqId': 1, 'contract': 1881316957920: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-05 06:07:48,historicalDataEnd,"reqId: 1, start: ******** 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
2025-08-05 06:09:08,nextValidId,orderId: 228
2025-08-05 06:09:08,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2222581025824: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 D', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-05 06:09:09,historicalDataEnd,"reqId: 1, start: ******** 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
2025-08-05 06:09:55,nextValidId,orderId: 228
2025-08-05 06:09:55,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2240056204688: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-05 06:09:56,historicalDataEnd,"reqId: 1, start: 20240729 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
2025-08-05 06:13:59,nextValidId,orderId: 228
2025-08-05 06:13:59,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2143737688000: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 y', 'barSizeSetting': '1 day', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-05 06:14:00,historicalDataEnd,"reqId: 1, start: 20240729 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
2025-08-05 06:14:51,nextValidId,orderId: 228
2025-08-05 06:14:51,Req reqHistoricalData,"() {'reqId': 1, 'contract': 2802460730400: 0,TSLA,STK,,,0,,,SMART,NASDAQ,USD,,,False,,,,combo:, 'endDateTime': '******** 20:00:00 US/Eastern', 'durationStr': '1 y', 'barSizeSetting': '5 mins', 'whatToShow': 'TRADES', 'useRTH': 1, 'formatDate': 1, 'keepUpToDate': False, 'chartOptions': []}"
2025-08-05 06:16:02,historicalDataEnd,"reqId: 1, start: 20240729 20:00:00 US/Eastern, end: ******** 20:00:00 US/Eastern"
