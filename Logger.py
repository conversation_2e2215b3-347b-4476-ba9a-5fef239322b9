import sys, subprocess, threading
from enum import IntEnum
import json
from datetime import datetime
import csv
from PyQt6.QtCore import QTimer

class Terminal:
    def __init__(self, mode:str):
        # Windows 下新建控制台标志；Linux/Mac 去掉 creationflags 即可
        CREATE_NEW_CONSOLE = 0x00000010
    
        # cmd = [sys.executable, "ConsoleWorker.py", mode]
        cmd = [sys.executable, "ConsoleWorker.py", mode]

        self.proc = subprocess.Popen(
            cmd,
            stdin = subprocess.PIPE,
            creationflags = CREATE_NEW_CONSOLE
        )
        
        self._lock = threading.Lock()

    def print(self,  message:str):
        # text = f"{datetime.now():%Y-%m-%d %H:%M:%S} {caller}: {message}\n".encode("utf-8")
        text = f"{message}\n".encode("utf-8")
        with self._lock:
            try:
                self.proc.stdin.write(text)
                self.proc.stdin.flush()
            except Exception as e:
                print(f"Logger Terminal print error: {e}")                

class Logger(): 
    class Level(IntEnum):
        GENERAL  = 0
        DETAIL   = 1
        DATA = 2
        UPDATED_DATA = 3

    def __init__(self, config:dict, isDebug:bool):
        self.config = config
        self.tradeHistoryFile = self.config["TradeHistoryFile"]

        self.logFiles = [self.config["LogGeneralFile"],self.config["LogDetailFile"],self.config["LogDataFile"],self.config["LogUpdateDataFile"]]
        
        self.isDebug = isDebug
        self.mainWindow = None

        if  self.isDebug:
            self.terminals = [
                Terminal("general"),
                Terminal("detail"),
                Terminal("data"),
                Terminal("updated_data")
            ]
        
        

    def setUILogDisplay(self, mainWindow):
        self.mainWindow = mainWindow

    def log(self, caller:str, message:str, level:Level):

        _time = f"{datetime.now():%Y-%m-%d %H:%M:%S}"

        text = f"{_time} {caller}: {message}"

        if  level == Logger.Level.GENERAL and self.mainWindow is not None:
            self.mainWindow.mainThreadAppendMessage.emit(text)
       

        if  self.isDebug:
            self.terminals[level].print(text)

        _row =  [_time, caller, message]
        self.writeLog(self.logFiles[level], _row)



    def writeLog(self, file, data):
        with open(file, mode='a', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile,quoting=csv.QUOTE_MINIMAL)

            if csvfile.tell() == 0:
                writer.writerow(["Time","Caller","Message"])

            writer.writerow(data)
        
    

    def logTradeHistory(self  ,trade):
    
        with open(self.tradeHistoryFile, "a", encoding="utf-8") as f:
            f.write(json.dumps(trade, ensure_ascii=False) + "\n")


    def __del__(self):
        if  self.isDebug:
            for terminal in self.terminals:
                terminal.proc.stdin.close()



