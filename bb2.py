import pandas as pd
import numpy as np
import pandas_market_calendars as mcal
import os
from PyQt6.QtCore import QUrl, pyqtSlot, pyqtSignal  # Add this import if you are using PyQt5
from PyQt6.QtWebEngineWidgets import QWebEngineView  # Add this import for QWebEngineView
import json
import sys
from PyQt6.QtWidgets import QApplication



from datetime import datetime
# from zoneinfo import ZoneInfo
import zoneinfo
# from PySide2.QtCore import QUrl  # Or use this line if you are using PySide2

# -----------------------------------------------------------------------------
# 1. SCALE DOWN A SINGLE BAR
# -----------------------------------------------------------------------------
def scale_down_bar(bar: dict,
                   start_time: pd.Timestamp,
                   end_time: pd.Timestamp,
                   freq: str,
                   high_frac: float = 0.25,
                   low_frac: float = 0.75) -> pd.DataFrame:
    # 1) original price curve
    timeline = pd.to_datetime([
        start_time,
        start_time + (end_time - start_time) * high_frac,
        start_time + (end_time - start_time) * low_frac,
        end_time
    ])
    prices = [bar["Open"], bar["High"], bar["Low"], bar["Close"]]
    curve = pd.Series(prices, index=timeline).sort_index()

    # 2) build sub-index aligned IB-style when freq is hourly
    sub_index = None
    if freq.endswith("h") or freq.endswith("H"):
        to_n = pd.Timedelta(freq)
        # first bar starts exactly at market open
        first = start_time
        # compute the next top-of-hour after start_time
        # e.g. 09:30 + 1h → 10:30, then replace minute=0 → 10:00
        next_hr = (start_time + to_n).replace(minute=0, second=0)
        rest = pd.date_range(next_hr, end_time, freq=freq)
        sub_index = pd.DatetimeIndex([first]).append(rest)
        # if the last isn’t exactly end_of_session, append it
        if sub_index[-1] < end_time:
            sub_index = sub_index.append(pd.DatetimeIndex([end_time]))
    else:
        # fallback to your old logic for other freqs
        sub_index = pd.date_range(start_time, end_time, freq=freq)
        if sub_index[-1] < end_time:
            sub_index = sub_index.append(pd.DatetimeIndex([end_time]))

    # 3) interpolate the “curve” at all our new points
    curve = (
        pd.Series(prices, index=timeline)
          .sort_index()
          .reindex(curve.index.union(sub_index))
          .interpolate(method="time")
          .loc[sub_index]
    )

    # 4) build OHLC for each segment
    records = []
    step = len(sub_index) - 1
    for t0, t1 in zip(sub_index[:-1], sub_index[1:]):
        seg = curve[t0:t1]
        if seg.empty:
            continue
        records.append({
            "date":     t0,
            "Open":     seg.iloc[0],
            "High":     seg.max(),
            "Low":      seg.min(),
            "Close":    seg.iloc[-1],
            "Volume":   bar["Volume"]   / step,
            "WAP":      bar["WAP"],
            "BarCount": bar["BarCount"] / step,
        })

    return pd.DataFrame(records).set_index("date")


def resample_bars(df: pd.DataFrame,
                  from_freq: str,
                  to_freq: str,
                  end_time: pd.Timestamp = None,
                  tradingTimeStart: str = "09:30",
                  tradingTimeEnd: str = "16:00") -> pd.DataFrame:
    """
    Resample bars from `from_freq` to `to_freq`, with optional session trading hours.
    
    For upsampling, uses tradingTimeStart and tradingTimeEnd as custom intraday boundaries.
    For downsampling, standard pandas aggregation is used.
    """
    df = df.copy()
    df.index = pd.to_datetime(df.index)

    # Optionally trim the time window
    if end_time is not None:
        df = df[df.index <= pd.to_datetime(end_time)]

    from_n = pd.Timedelta(from_freq)
    to_n = pd.Timedelta(to_freq)

    # UPSAMPLE
    if to_n < from_n:
        pieces = []
        for ts, row in df.iterrows():
            date = ts.date()
            bar_start = pd.Timestamp(f"{date} {tradingTimeStart}")
            bar_end   = pd.Timestamp(f"{date} {tradingTimeEnd}")

            pieces.append(scale_down_bar(row.to_dict(), bar_start, bar_end, to_freq))
        return pd.concat(pieces).sort_index()

    # DOWNSAMPLE
    else:
        ohlc_dict = {
            "Open": "first", "High": "max", "Low": "min", "Close": "last",
            "Volume": "sum", "BarCount": "sum"
        }
        df["WAPxV"] = df["WAP"] * df["Volume"]
        agg = df.resample(to_freq).agg(ohlc_dict)
        agg["WAP"] = agg["WAPxV"] / agg["Volume"]
        return agg.drop(columns="WAPxV")



# -----------------------------------------------------------------------------
# EXAMPLE USAGE
# -----------------------------------------------------------------------------
if __name__ == "__main__":
    


    def build_chart_data_lc( data):
        if len(data) == 0:
            return [], []


        make_time_func = make_chart_time(data[0]["date"])

        priceData = []
        volumeData = []
        for record in data:
            priceData.append(
                    {"time":make_time_func(record["date"]), "open": record["open"], "high": record["high"], "low": record["low"], "close": record["close"]}
                )

            volumeData.append(
                {"time": make_time_func(record["date"]), "value": record["volume"], "color": '#ef5350' if record["open"] > record["close"] else  '#26a69a'}
            )  
        
        return priceData, volumeData



    def make_chart_time(dateStr):
        parts = dateStr.split()
        
        # Case A: just YYYYMMDD
        if len(parts) == 1:
            return lambda d: f"{d[:4]}-{d[4:6]}-{d[6:8]}"
        
        # Case B: YYYYMMDD + HH:MM:SS  → naïve timestamp
        if len(parts) == 2:
            fmt = "%Y%m%d %H:%M:%S"
            return lambda d: int(datetime.strptime(d, fmt).timestamp())
        
        # Case C: YYYYMMDD + HH:MM:SS + TZ
        date_part, time_part, tz_name = parts
        tz = zoneinfo.ZoneInfo(tz_name)
        fmt = "%Y%m%d %H:%M:%S"
        def make_time(d):
            dt = datetime.strptime(d[:17], fmt).replace(tzinfo=tz)
            return int(dt.timestamp())
        return make_time



    


   
    def Handle_OnChartViewReady(chartview:QWebEngineView, data):
        priceData = []
        volumeData = []
        
       
        priceData, volumeData = build_chart_data_lc(data)



        priceData_json = json.dumps(priceData)
        volumeData_json = json.dumps(volumeData)


        with open('chart/chart_template.html', 'r', encoding='utf-8') as f:
            template = f.read()

        time_visible = "true" 
        seconds_visible = "true" 
            
        template = template.replace('<!-- TIME_VISIBLE_PLACEHOLDER -->', time_visible)
        template = template.replace('<!-- SECONDS_VISIBLE_PLACEHOLDER -->', seconds_visible)
        # template = template.replace('<!-- SYMBOL_PLACEHOLDER -->', con.symbol)
        template = template.replace('<!-- PRICE_DATA_PLACEHOLDER -->', priceData_json)
        template = template.replace('<!-- VOLUME_DATA_PLACEHOLDER -->', volumeData_json)

        # with open(f"chart/temp/chart_{chartWidget.chart_name}.html", 'w', encoding='utf-8') as f:
        #     f.write(template)

        base_path = os.path.abspath("chart") + os.path.sep
        base_url = QUrl.fromLocalFile(base_path)
        chartview.setHtml(template,base_url)

    
    # --- build sample data ---
    raw = [
        {"date": "2025-07-28", "Open":4323,"High":4550,"Low":4123,"Close":4223,
        "Volume":200,"WAP":4323,"BarCount":2}
        
    ]
    df_day = pd.DataFrame(raw).set_index("date")
    df_day.index = pd.to_datetime(df_day.index)

    # resample daily → hourly
    df_hour = resample_bars(df_day, from_freq="1D", to_freq="1h")

    # build our list
    data_list = (
        df_hour
        .reset_index()
        .rename(columns={"date":"date","Open":"open","High":"high","Low":"low","Close":"close","Volume":"volume"})
        .assign(date=lambda d: d["date"].dt.strftime("%Y%m%d %H:%M:%S"))
        .to_dict(orient="records")
    )



    app = QApplication(sys.argv)
    chartView = QWebEngineView(None)
    Handle_OnChartViewReady(chartView, data_list)      
    chartView.show()
    sys.exit(app.exec())
     

    