from typing import Generic, TypeVar, List

T = TypeVar("T")

class BufferList(Generic[T]):
    def __init__(self, max_size=10000, init_data=None):
        """
        初始化环形缓冲区，该缓冲区最多存储 max_size 条数据。
        可选参数 init_data 为初始数据列表，若数据条数超过 max_size，
        则只保留最后 max_size 条数据。
        """
        self.MAX_SIZE = max_size
        self.buffer = [None] * max_size   # 预分配固定大小的列表
        self.current_index = 0            # 下一个写入位置
        self.current_size = 0             # 当前有效数据数量

        if init_data is not None:
            # 如果初始数据超过最大容量，则只取最后 max_size 条记录
            if len(init_data) > max_size:
                init_data = init_data[-max_size:]
                
            self.current_size = len(init_data)
            self.current_index = self.current_size % self.MAX_SIZE

            # 将初始数据填入 buffer，未使用的部分仍为 None
            for i, row in enumerate(init_data):
                self.buffer[i] = row

    def add_data(self, new_row):
        """添加一条新数据。如果缓冲区已满，则覆盖最旧的数据。"""
        self.buffer[self.current_index] = new_row
        self.current_index = (self.current_index + 1) % self.MAX_SIZE
        if self.current_size < self.MAX_SIZE:
            self.current_size += 1

    def get_data(self, index)-> T:
        """
        根据索引返回数据。支持正数和负数索引，
        正数索引 0 表示最旧的数据，-1 表示最新的数据。
        """
        # 如果 index 为负数，转换为正数索引
        if index < 0:
            index += self.current_size
        if index < 0 or index >= self.current_size:
            raise IndexError("索引超出有效数据范围")

        # 如果缓冲区已满，最旧数据在 current_index 位置，否则从 0 开始
        start = self.current_index if self.current_size == self.MAX_SIZE else 0
        actual_index = (start + index) % self.MAX_SIZE
        return self.buffer[actual_index]

    def set_data(self, index: int, new_row):
        """
        用 new_row 覆盖逻辑索引 index 对应的数据。
        支持正/负索引：0 表示最旧，-1 表示最新。
        """
        # 1) normalize negative indices
        if index < 0:
            index += self.current_size
        # 2) bounds check
        if index < 0 or index >= self.current_size:
            raise IndexError("索引超出有效数据范围")

        # 3) find actual slot in the ring
        start = self.current_index if self.current_size == self.MAX_SIZE else 0
        actual_idx = (start + index) % self.MAX_SIZE

        # 4) overwrite
        self.buffer[actual_idx] = new_row

    def __setitem__(self, index, new_row):
        self.set_data(index, new_row)
        
    def to_list(self)-> List[T]:
        """
        返回一个列表，包含当前所有有效数据，顺序为从最旧到最新，
        该输出列表可以供 Pandas DataFrame 构造函数使用。
        """
        return [self.get_data(i) for i in range(self.current_size)]

    def __len__(self):
        """返回缓冲区中当前有效的数据条数。"""
        return self.current_size

    def __str__(self):
        """返回缓冲区中所有有效数据组成的列表字符串。"""
        return str(self.to_list())


    def __iter__(self):
        for i in range(self.current_size):
            yield self.get_data(i)



if __name__ == '__main__':
    import pandas as pd

    records =  []

    for i in range(1, 21):
        records.append({
            "Unnamed: 0": i,
            "date": f"2023-02-02 09:00:0{i % 60:02d}+00:00",
            "open": 147.14 + i,
            "high": 147.49 + i,
            "low": 146.56 + i,
            "close": 147.0 + i,
            "volume": 79604.0 + i * 100
        })


    # 初始化缓冲区，容量设置为 10,000（这里为了测试使用 10 个数据）
    rb = BufferList(max_size=10, init_data=records)



    # 用 to_list() 方法输出 DataFrame 所需的列表
    data_list = rb.to_list()
    print("环形缓冲区数据列表：")
    for record in data_list:
        print(record)

    # 用 Pandas 创建 DataFrame（顺序为最旧到最新的数据）
    df = pd.DataFrame(data_list)
    print("\nPandas DataFrame：")
    print(df)
    # records =  []
