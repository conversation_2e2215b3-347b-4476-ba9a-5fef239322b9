import pandas as pd
import numpy as np
import pandas_market_calendars as mcal

# -----------------------------------------------------------------------------
# 1. SCALE DOWN A SINGLE BAR
# -----------------------------------------------------------------------------
def scale_down_bar(bar: dict,
                   start_time: pd.Timestamp,
                   end_time: pd.Timestamp,
                   freq: str,
                   high_frac: float = 0.25,
                   low_frac: float = 0.75) -> pd.DataFrame:
    """
    Splits one bar into smaller bars at frequency `freq`.
    
    bar: {
      "Open": float, "High": float, "Low": float, "Close": float,
      "Volume": float, "WAP": float, "BarCount": int
    }
    start_time, end_time: window of this bar
    freq: e.g. "1H", "30T"
    high_frac, low_frac: where High/Low occur as fraction of the interval
    """
    # 1) build piecewise-linear time–price “curve”
    timeline = pd.to_datetime([start_time,
                                start_time + (end_time - start_time) * high_frac,
                                start_time + (end_time - start_time) * low_frac,
                                end_time])
    prices   = [bar["Open"], bar["High"], bar["Low"], bar["Close"]]
    curve    = pd.Series(data=prices, index=timeline).sort_index()
    curve    = curve.resample(freq).interpolate("time")
    print (" curve:", curve)
    # 2) for each sub-bar interval, compute OHLC

    print("datetime",start_time, end_time)
    sub_index = pd.date_range(start_time, end_time, freq=freq)
    print (" sub_index:", sub_index)
    records = []

    print (f" zip:{sub_index[:-1]}, {sub_index[1:]}")
    for t0, t1 in zip(sub_index[:-1], sub_index[1:]):
        seg = curve[t0:t1]
        if seg.empty: continue
        rec = {
            "Date": t0,
            "Open": float(seg.iloc[0]),
            "High": float(seg.max()),
            "Low":  float(seg.min()),
            "Close":float(seg.iloc[-1]),
            # distribute volume/WAP/BarCount evenly:
            "Volume": bar["Volume"]   / len(sub_index)-1,
            "WAP":    bar["WAP"],  # or recompute from seg, if you like
            "BarCount": bar["BarCount"] / len(sub_index)-1
        }
        records.append(rec)
    return pd.DataFrame(records).set_index("Date")


# -----------------------------------------------------------------------------
# 2. SCALE UP OR DOWN A BATCH OF BARS UNTIL AN OPTIONAL END TIME
# -----------------------------------------------------------------------------
def resample_bars(df: pd.DataFrame,
                  from_freq: str,
                  to_freq:   str,
                  end_time:  pd.Timestamp = None) -> pd.DataFrame:
    """
    Resample a DataFrame of bars from `from_freq` to `to_freq`.
    
    If to_freq is higher resolution (e.g. "1D" -> "1H"), each original bar is
    split by scale_down_bar.  If to_freq is lower resolution (e.g. "15T" -> "1H"),
    uses pandas’ .resample().aggregate().
    
    df index must be a DatetimeIndex at uniform `from_freq`.
    """
    df = df.copy()
   
    df.index = pd.to_datetime(df.index)
   
    # optionally trim
    if end_time is not None:
        df = df[df.index <= pd.to_datetime(end_time)]
    
    from_n = pd.Timedelta(from_freq)
   
    to_n   = pd.Timedelta(to_freq)
 
  


    # UPSAMPLE (higher resolution target)
    if to_n < from_n:
        pieces = []
        for ts, row in df.iterrows():
            bar_start = ts
            bar_end   = ts + from_n
            pieces.append(scale_down_bar(row.to_dict(),
                                         bar_start, bar_end, to_freq))
        return pd.concat(pieces).sort_index()
    
    # DOWNSAMPLE (lower resolution target)
    else:
        ohlc_dict = {
            "Open":  "first",
            "High":  "max",
            "Low":   "min",
            "Close": "last",
            "Volume":"sum",
            "BarCount":"sum"
        }
        # recompute WAP as volume‐weighted average of original WAP*Volume
        df["WAPxV"] = df["WAP"] * df["Volume"]
        ag = df.resample(to_freq).agg(ohlc_dict)
        ag["WAP"] = ag["WAPxV"] / ag["Volume"]
        return ag.drop(columns="WAPxV")
# import pandas as pd
# import pandas_market_calendars as mcal

cal = mcal.get_calendar('NYSE')  # Define the calendar, e.g., for NYSE
raw = cal.schedule(start_date='2025-01-01', end_date='2025-12-31', market_times='all')

intraday_everything = mcal.date_range(
    raw,
    frequency='1H',
    session={"pre", "RTH", "break", "post"}
).tz_convert('US/Eastern')

print(intraday_everything)

# 1. 构造带时区的 schedule DataFrame，索引仍是原始 UTC Timestamp
sched = pd.DataFrame({
    'open':  raw['market_open'].dt.tz_convert('America/New_York'),
    'close': raw['market_close'].dt.tz_convert('America/New_York'),
}, index=raw.index)



# def resample_bars(df: pd.DataFrame, from_freq: str='1D', to_freq: str='1h'):
    
#     from_delta = pd.Timedelta(from_freq)
#     to_delta   = pd.Timedelta(to_freq)

    
#     if to_delta < from_delta:
#         parts = []
        
#         for ts, row in df.iterrows():
#         # ts 是一个 Timestamp，先取日期，再 localize 成与 sched.index 相同的时区
#             session_key = pd.Timestamp(ts.date()).tz_localize(sched.index.tz)
#             sess = sched.loc[session_key]  # 精确索引

#             parts.append(
#                 scale_down_bar(
#                     bar=row.to_dict(),
#                     start_time=sess['open'],
#                     end_time=sess['close'],
#                     freq=to_freq
#                 )
#             )

#         # 拼回一个 DataFrame
#         return pd.concat(parts).sort_index()

#     else:
#         ohlc = {
#             'Open':     'first',
#             'High':     'max',
#             'Low':      'min',
#             'Close':    'last',
#             'Volume':   'sum',
#             'BarCount': 'sum'
#         }
#         df['WAPxV'] = df['WAP'] * df['Volume']
#         agg = df.resample(to_freq, label='left', closed='left').agg(ohlc)
#         agg['WAP'] = agg['WAPxV'] / agg['Volume']
#         return agg.drop(columns='WAPxV').tz_localize(None)


import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
# --- (include your scale functions here: scale_down_bar, resample_bars, etc.) ---

def plot_ohlc(ax, df, width, color_up, color_down, alpha=1.0):
    """
    Draw OHLC candles on ax.
    df must have DatetimeIndex and columns Open, High, Low, Close.
    width is in days (as float), so e.g. 0.8 for 80% of one day, 1/24 for 1 hour.
    """
    # convert datetime to matplotlib float days
    dates = mdates.date2num(df.index.to_pydatetime())
    for date, o, h, l, c in zip(dates,
                                df['Open'], df['High'],
                                df['Low'], df['Close']):
        color = color_up if c >= o else color_down
        # wick
        ax.vlines(date, l, h, color=color, alpha=alpha)
        # body
        left = date - width/2
        rect = plt.Rectangle((left, min(o, c)),
                             width, abs(c - o),
                             facecolor=color, alpha=alpha)
        ax.add_patch(rect)

    # format
    ax.xaxis_date()
    ax.grid(True)
# -----------------------------------------------------------------------------
# EXAMPLE USAGE
# -----------------------------------------------------------------------------
if __name__ == "__main__":
    # raw = [
    #     {"Date": "2025-07-28", "Open":4100,"High":4323,"Low":4050,"Close":4200,
    #      "Volume":200,"WAP":4323,"BarCount":2},
    #     {"Date": "2025-07-29", "Open":4323,"High":4323,"Low":4323,"Close":4323,
    #      "Volume":200,"WAP":4323,"BarCount":2},
    # ]
    # df = pd.DataFrame(raw).set_index("Date")
    # df.index = pd.to_datetime(df.index)
    
    # # 1. One-day bar → hourly bars
    # print(resample_bars(df, from_freq="1D", to_freq="1H"))
    
    # # # 2. Hourly bars → daily bars
    # # #    you could also stop at a specific date:
    # # print(resample_bars(df_hourly, from_freq="1H", to_freq="1D", end_time="2025-07-29"))
    
    # # 3. Scale a single bar:
    # single = df.iloc[0].to_dict()
    # bartime = df.index[0]
    # print(scale_specific_bar(single, "1D", "30T", bartime))







    # --- build sample data ---
    raw = [
        {"Date": "2025-07-28", "Open":4323,"High":4550,"Low":4123,"Close":4123,
        "Volume":200,"WAP":4323,"BarCount":2}
        
    ]
    df_day = pd.DataFrame(raw).set_index("Date")
    df_day.index = pd.to_datetime(df_day.index)

    # resample daily → hourly
    df_hour = resample_bars(df_day, from_freq="1D", to_freq="1h")

    # --- plotting ---
    fig, ax = plt.subplots(figsize=(12, 4))

    # original bars – width = 0.8 day
    plot_ohlc(ax, df_day, width=0.8, color_up="lightgrey", color_down="lightgrey", alpha=0.7)

    # resampled bars – width = 1 hour ≈ 1/24 day
    plot_ohlc(ax, df_hour, width=1/24*0.8, color_up="steelblue", color_down="tomato", alpha=0.9)

    ax.set_title("Grey = Daily Bars (Input)   Blue/Red = Hourly Bars (Output)")



    plt.tight_layout()
    plt.show()

    