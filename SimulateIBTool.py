from ibapi.contract import Contract
from ibapi.order import Order
from Comm import Contract
from IBTool import *
import pandas as pd

class SimulateIBTool(IBTool):
    def __init__(self, config):
        super().__init__(config)
        self.simulatedOrders = []


    def getSimulatedHistoricalData(self)-> pd.DataFrame:
        return pd.DataFrame([])

    def feedSimulatedHistoricalData(self, reqId: int,  data: pd.DataFrame , startDate:str, ):
        if startDate != "":
        
            _data =  data[data.index < startDate]
            for index, row in _data.iterrows():
                barData = BarData()
                
                barData.date = row["date"]
                barData.open = row["open"]
                barData.high = row["high"]
                barData.low = row["low"]
                barData.close = row["close"]
                barData.volume = row["volume"]
                barData.wap = row["wap"]
                barData.barCount = row["barCount"]
                
                self.historicalData(reqId, barData)
        
        self.historicalDataEnd(reqId, "", "")
        
    def reqHistoricalData(self, reqId : int, contract: Contract, endDateTime: str, durationStr: str, barSizeSetting: str, whatToShow: str, useRTH: int, formatDate: int, keepUpToDate: bool, chartOptions: ListOfTagValue):
        timeout = 10 if keepUpToDate  else 0
        parms = {"contract": contract, "endDateTime": endDateTime, "durationStr": durationStr, "barSizeSetting": barSizeSetting, "whatToShow": whatToShow, "useRTH": useRTH, "formatDate": formatDate, "keepUpToDate": keepUpToDate, "chartOptions": chartOptions}
        req = self.register_requests(type = REQUEST_TYPES.GET_HISTORICAL_DATA,  reqId = reqId, keepAlive=keepUpToDate, timeout=timeout, parms=parms)
        
        data = self.getSimulatedHistoricalData()

        tread = Thread(target=self.feedSimulatedHistoricalData, args=(reqId, data, endDateTime), name="Feed Simulated Historical Data")
        tread.start()
        return req

    def cancelHistoricalData(self, reqId):
        delReq = self.find_request_by_type_list([REQUEST_TYPES.GET_HISTORICAL_DATA,  REQUEST_TYPES.GET_HISTORICAL_TICKS],reqId)
        if delReq is not None:
            delReq.pendingDelete = True
       
        req = self.register_requests(type = REQUEST_TYPES.CANCEL_HISTORICAL_DATA, reqId = reqId, timeout=0.01)

        return req
    

    def feedSimulatedAccountData(self, reqId: int):
        
        accountData:list = []
        for item in accountData:
            self.updateAccountValue(item["key"], item["val"], item["currency"], item["accountName"])


        portfolioData:list = []
        for item in portfolioData:
            self.updatePortfolio(item["contract"], item["position"], item["marketPrice"], item["marketValue"], item["averageCost"], item["unrealizedPNL"], item["realizedPNL"], item["accountName"])


        accountName = accountData[0]["accountName"]
        self.accountDownloadEnd(accountName)


    def reqAccountUpdates(self, accountCode: str):
        self.accountData = {}
        self.protfolioData = {}
        self.accountUpdateTime = ""

        req2 = self.register_requests( type = REQUEST_TYPES.UPDATE_ACCOUNT, keepAlive=True )
        
        tread = Thread(target=self.feedSimulatedAccountData, args=(req2.reqId,), name="Feed Simulated Account Data")
        tread.start()

        return req2
    

    def cancelAccountUpdates(self, accountCode: str):
        delReq = self.find_request_by_type_list( [REQUEST_TYPES.UPDATE_ACCOUNT],-1)
        if delReq is not None:
            delReq.pendingDelete = True
        
        req2 = self.register_requests( type = REQUEST_TYPES.CANCEL_UPDATE_ACCOUNT, timeout=0.01 , noResponse=True)

        return req2
    

    def placeOrder(self, contract: Contract, order: Order):
        parms = {"contract": contract, "order": order}
        req = self.register_requests(type = REQUEST_TYPES.PLACE_ORDER, reqId = self.nextOrderId,timeout=10, delayWhenFinished=0.1, parms=parms)
        # super().placeOrder(self.nextOrderId, contract, order)
        
        self.simulatedOrders.append({"orderId": self.nextOrderId, "contract": contract, "order": order})
        
        self.nextOrderId += 1
        return req
    
    def cancelOrder(self, orderId: int ):
        cancelOrder = OrderCancel()
        cancelOrder.manualOrderCancelTime = ""

        parms = {"orderId": orderId, "cancelOrder": cancelOrder} 
        req =self.register_requests(type = REQUEST_TYPES.CANCEL_ORDER, reqId = orderId, timeout=10, delayWhenFinished=0.1 , parms=parms)

        for order in self.simulatedOrders:
            if order["orderId"] == orderId:
                self.simulatedOrders.remove(order)
                break

        return req
