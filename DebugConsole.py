
import sys
import code
import threading
from PyQt6.QtCore import QObject
from PyQt6.QtWidgets import QApplication
from TradeTool import TradeTool    # your existing class
import Global_Values

class TradeToolConsole(code.InteractiveConsole):
    def __init__(self, trade_tool):
        super().__init__({'tradeTool': trade_tool})
        self.trade_tool = trade_tool

    def push(self, line):
        if line.strip().startswith('.'):
            # rewrite ".ib.requests" → "tradeTool.ib.requests"
            line = 'tradeTool' + line
        return super().push(line)


if __name__ == '__main__':
    Global_Values.initConfig()
    tradeTool = TradeTool()
    
    # prepare your console
    banner = (
        "🔍 Debug console—type .ib.requests or any Python expression.\n"
        "Press Ctrl-D to quit."
    )
    console = TradeToolConsole(tradeTool)

    # spawn the console in a background thread
    threading.Thread(
        target=lambda: console.interact(banner),
        daemon=True
    ).start()

    # now start your GUI event loop on the main thread
    sys.exit(tradeTool.run())