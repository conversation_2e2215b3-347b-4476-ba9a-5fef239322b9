import traceback
import functools
import re


def monitor_method(func):
    """
    裝飾器：保持原業務方法不做額外輸出，僅返回方法本身。
    （這裡用作身份裝飾器）
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)
    return wrapper

class MonitorMeta(type):
    """
    元類：自動將類中所有非魔術業務方法（名稱不以 __ 包圍的方法）
    用 monitor_method 裝飾器包裝，實際上保持方法原樣。
    """
    def __new__(cls, name, bases, namespace):
        for attr, val in list(namespace.items()):
            if callable(val) and not (attr.startswith("__") and attr.endswith("__")):
                namespace[attr] = monitor_method(val)
        return super().__new__(cls, name, bases, namespace)

class MonitorMixin(metaclass=MonitorMeta):
    
    """
    通用監控混入類：
      - __init__ 與 __del__ 僅輸出提示信息，不打印調用棧；
      - 在 __getattribute__ 中：若存取的屬性名稱為系統屬性 (以 __ 開頭且以 __ 結尾)，
        直接返回；否則先取得屬性。
        如果屬性是可調用對象（方法），直接返回，不打印任何提示與調用棧；
        否則（即非方法屬性）在 _monitoring_enabled 為 True 時打印提示與調用棧。
      - __setattr__ 對於屬性修改，若 _monitoring_enabled 為 True，則打印提示與調用棧。
    """

    # 將監控工具內部方法或系統方法的名稱放入過濾集合，
    # 這些方法的堆疊不作顯示
    INTERNAL_METHODS = {
        "__init__",
        "__del__",
        "__getattribute__",
        "__setattr__",
        "print_stack",
        "wrapper"
    }


    def __init__(self, *args, **kwargs):
        object.__setattr__(self, "_monitoring_enabled", False)
        print(f"--- {self.__class__.__name__} 物件創建 ---")
        super().__init__(*args, **kwargs)
        object.__setattr__(self, "_monitoring_enabled", True)
    
    def __getattribute__(self, name):
        # 系統屬性直接返回
        if name.startswith("__") and name.endswith("__"):
            return object.__getattribute__(self, name)
        attr = object.__getattribute__(self, name)
        # 如果屬性為可調用對象（例如業務方法），則不在此層打印
        if callable(attr):
            return attr
        monitoring = object.__getattribute__(self, "__dict__").get("_monitoring_enabled", True)
        if monitoring:
            print(f"獲取屬性：{name}")
            MonitorMixin.print_stack(limit=5, reverse=True)
        return attr
    
    def __setattr__(self, name, value):
        if name.startswith("__") and name.endswith("__"):
            object.__setattr__(self, name, value)
            return
        monitoring = object.__getattribute__(self, "__dict__").get("_monitoring_enabled", True)
        if monitoring:
            print(f"設置屬性：{name} = {value}")
            MonitorMixin.print_stack(limit=5, reverse=True)
        object.__setattr__(self, name, value)
    
    def __del__(self):
        print(f"--- {self.__class__.__name__} 物件銷毀 ---")

    @staticmethod
    def print_stack(limit=5, reverse=False):
        """
        取得最近 limit 層的調用棧，過濾掉 INTERNAL_METHODS 中的方法，
        並將每一層的文件、行數、函數名以及觸發調用的代碼顯示在同一行。
        若 reverse 為 True，則反轉順序，使最新的呼叫顯示在最前面。
        """
        # 取得結構化堆疊（排除本函數自身）
        stack = traceback.extract_stack()[:-1]
        # 過濾掉屬於 INTERNAL_METHODS 的幀
        filtered = [frame for frame in stack if frame.name not in MonitorMixin.INTERNAL_METHODS]
        if limit is not None:
            filtered = filtered[-limit:]
        if reverse:
            filtered = list(reversed(filtered))
        print("調用棧:")
        for frame in filtered:
            # 若 frame.line 不為空，則將其strip掉前後空白，否則使用空字符串
            line_content = frame.line.strip() if frame.line else ""
            print(f'  File "{frame.filename}", line {frame.lineno}, in {frame.name}: {line_content}')        



from PyQt6.QtWidgets import QPushButton, QWidget, QFrame
from datetime import datetime

def print_filtered_stack_trace(max_levels=5):
    """
    取得最近 limit 層的調用棧，過濾掉 INTERNAL_METHODS 中的方法，
    並將每一層的文件、行數、函數名以及觸發調用的代碼顯示在同一行。
    若 reverse 為 True，則反轉順序，使最新的呼叫顯示在最前面。
    """
    # 取得結構化堆疊（排除本函數自身）
    stack = traceback.extract_stack()[:-1]
    # 過濾掉屬於 INTERNAL_METHODS 的幀
    filtered = [frame for frame in stack if frame.name not in MonitorMixin.INTERNAL_METHODS]
    reverse = True
    if max_levels is not None:
        filtered = filtered[-max_levels:]
    if reverse:
        filtered = list(reversed(filtered))
    print("調用棧:")
    for frame in filtered:
        # 若 frame.line 不為空，則將其strip掉前後空白，否則使用空字符串
        line_content = frame.line.strip() if frame.line else ""
        print(f'  File "{frame.filename}", line {frame.lineno}, in {frame.name}: {line_content}')      



    # """
    # 打印調用棧信息，只顯示與用戶代碼相關的部分，最多顯示 max_levels 層，
    # 並過濾掉包含 'PyQt6' 的幀。
    # """
    # stack = traceback.extract_stack()[:-1]  # 去除當前函數層
    # filtered = [frame for frame in stack if "PyQt6" not in frame.filename]
    # if len(filtered) > max_levels:
    #     filtered = filtered[-max_levels:]
    # print("***** 調用棧（僅顯示用戶代碼，最多 {} 層） *****".format(max_levels))
    # for frame in filtered:
    #     line_content = frame.line.strip() if frame.line else ""
    #     print(f'  文件 "{frame.filename}", 第 {frame.lineno} 行, 函數 {frame.name}: {line_content}')

class MonitorPushButton(QPushButton):
    """
    監控 QPushButton：
      - 在屬性讀取、方法調用以及屬性設置時打印提示與調用棧（最多 5 層用戶代碼）。
      - 但對於信號類屬性（例如 clicked）則不做包裝，以免影響正常使用。
    """
    def __init__(self, text: str = "", parent: QWidget = None):
        super().__init__(text, parent)
        # 使用 object.__setattr__ 直接設置監控標誌，避免觸發 __setattr__ 監控
        object.__setattr__(self, "_monitoring_enabled", True)

    def __getattribute__(self, name: str):
        # 系統內部屬性直接返回
        if name.startswith("__") and name.endswith("__"):
            return super().__getattribute__(name)
        
        attr = super().__getattribute__(name)
        
        # 如果屬性具有 connect 方法，通常這意味著它是個信號對象，直接返回原屬性，不包裝
        if hasattr(attr, "connect"):
            return attr
        
        # 如果該屬性不是可調用對象（getter），打印提示及調用棧
        if not callable(attr):
            if object.__getattribute__(self, "_monitoring_enabled"):
                print(f">>> 訪問屬性: {name} time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")
                print_filtered_stack_trace(max_levels=5)
            return attr
        else:
            # 返回一個包裝後的函數，通過該函數調用原方法之前打印調用棧
            def wrapped(*args, **kwargs):
                if object.__getattribute__(self, "_monitoring_enabled"):
                    print(f">>> 調用方法: {name}() time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")
                    print_filtered_stack_trace(max_levels=5)
                return attr(*args, **kwargs)
            return wrapped

    def __setattr__(self, name: str, value):
        # 系統屬性直接交由父類處理
        if name.startswith("__") and name.endswith("__"):
            return super().__setattr__(name, value)
        if self.__dict__.get("_monitoring_enabled", False):
            print(f">>> 設置屬性: {name} = {value} time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")
            print_filtered_stack_trace(max_levels=5)
        super().__setattr__(name, value)


class MonitorQFrame(QFrame):

    def __init__(self, parent: QWidget = None):
        super().__init__( parent)
        # 使用 object.__setattr__ 直接設置監控標誌，避免觸發 __setattr__ 監控
        object.__setattr__(self, "_monitoring_enabled", True)

    def __getattribute__(self, name: str):
        # 系統內部屬性直接返回
        if name.startswith("__") and name.endswith("__"):
            return super().__getattribute__(name)
        
        attr = super().__getattribute__(name)
        
        # 如果屬性具有 connect 方法，通常這意味著它是個信號對象，直接返回原屬性，不包裝
        if hasattr(attr, "connect"):
            return attr
        
        # 如果該屬性不是可調用對象（getter），打印提示及調用棧
        if not callable(attr):
            if object.__getattribute__(self, "_monitoring_enabled"):
                print(f">>> 訪問屬性: {name} time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")
                print_filtered_stack_trace(max_levels=5)
            return attr
        else:
            # 返回一個包裝後的函數，通過該函數調用原方法之前打印調用棧
            def wrapped(*args, **kwargs):
                if object.__getattribute__(self, "_monitoring_enabled"):
                    print(f">>> 調用方法: {name}() time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")
                    print_filtered_stack_trace(max_levels=5)
                return attr(*args, **kwargs)
            return wrapped

    def __setattr__(self, name: str, value):
        # 系統屬性直接交由父類處理
        if name.startswith("__") and name.endswith("__"):
            return super().__setattr__(name, value)
        if self.__dict__.get("_monitoring_enabled", False):
            print(f">>> 設置屬性: {name} = {value} time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")
            print_filtered_stack_trace(max_levels=5)
        super().__setattr__(name, value)





if __name__=="__main__":

    class MyBusinessClass(MonitorMixin):
        def __init__(self, value):
            super().__init__()
            self.value = value

        def get_value(self):
        
            return self.value

        def set_value(self, new_val):
        
            self.value = new_val





    print("===== 創建物件 =====")
    obj = MyBusinessClass(42)

    print("\n===== 調用方法 get_value =====")
    val = obj.get_value()

    print("\n===== 調用方法 set_value (方式1) =====")
    obj.set_value(100)
    
    print("\n===== 調用方法 set_value (方式2: 屬性直接賦值) =====")
    obj.value = 123

    print("\n===== 刪除物件 =====")
    del obj
