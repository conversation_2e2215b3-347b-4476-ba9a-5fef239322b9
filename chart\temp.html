<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0"
    />
    <title>Lightweight Charts™ Customization Tutorial</title>
    <!-- Adding the standalone version of Lightweight charts -->
    <!--
	<script
      type="text/javascript"
      src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"
    ></script>
	-->
	
	<script
		type="text/javascript"
		src="lightweight-charts5.07.js">
	</script>
    <style>
      body {
        padding: 0;
        margin: 0;
        /* Add a background color to match the chart */
        background-color: #222;
      }
	  
	  #tooltip {
		  position: absolute;
		  top: 10px;
		  left: 10px;
		  z-index: 100;
		  background-color: rgba(0, 0, 0, 0);
		  padding: 4px 6px;
		  border-radius: 4px;
		  font-size: 14px;
		  pointer-events: none;
		  color: #DDD !important;
	 } 
	 
	 .price-up {
        color: #26a69a;
        font-family: Arial, sans-serif;
        font-size: 14px;
     }
      
     .price-down {
        color: #ef5350;
        font-family: Arial, sans-serif;
        font-size: 14px;
     }		 
    </style>
  </head>

  <body>
    <div id="container" style="position: absolute; width: 100%; height: 100%">    
		<div id="tooltip">
			<span id="symbol"><!-- SYMBOL_PLACEHOLDER --></span>&nbsp;&nbsp;&nbsp;&nbsp;
			 <span id="tooltip-data">Open: --      High: --      Low: --      Close: --      Volume: -- </span>     
		</div>
	</div>
    <script type="text/javascript">
	

	/** @type {import('lightweight-charts').IChartApi} */

	const chart = LightweightCharts.createChart(
		document.getElementById('container'),
		{
			layout: {
				background: { color: '#222' },
				textColor: '#DDD',
			},
			grid: {
				vertLines: { color: '#444' },
				horzLines: { color: '#444' },
			},
			crosshair: {
				mode: LightweightCharts.CrosshairMode.Normal,
			},
			timeScale: {
				timeVisible:    true,  // 顯示時分
				secondsVisible: true   // 顯示秒
			},
		}
	);

	const priceSeries = chart.addSeries(LightweightCharts.CandlestickSeries);

	priceSeries.priceScale().applyOptions({
		scaleMargins: {
			top: 0.1, // highest point of the series will be 70% away from the top
			bottom: 0.2,
		},
		
		//borderColor: '#888',
		//barSpacing: 2,
		
	});

	const volumeSeries = chart.addSeries(LightweightCharts.HistogramSeries, {
		color: '#26a69a',
		priceFormat: {
			type: 'volume',
		},
		priceScaleId: '', // set as an overlay by setting a blank priceScaleId

	});

	volumeSeries.priceScale().applyOptions({
		scaleMargins: {
			top: 0.85, // highest point of the series will be 70% away from the top
			bottom: 0,
		},
	});


	var priceData= [{"time": 1753781400, "open": 325.63, "high": 326.04333333333335, "low": 325.63, "close": 326.04333333333335}, {"time": 1753781700, "open": 326.04333333333335, "high": 326.04333333333335, "low": 325.12666666666667, "close": 325.12666666666667}, {"time": 1753782000, "open": 325.12666666666667, "high": 325.12666666666667, "low": 322.88, "close": 322.88}, {"time": 1753782300, "open": 322.88, "high": 322.88, "low": 320.6333333333333, "close": 320.6333333333333}, {"time": 1753782600, "open": 320.6333333333333, "high": 320.6333333333333, "low": 319.7733333333333, "close": 319.7733333333333}, {"time": 1753782900, "open": 319.7733333333333, "high": 320.3, "low": 319.7733333333333, "close": 320.3}, {"time": 1753783200, "open": 320.3, "high": 321.02666666666664, "low": 320.3, "close": 321.02666666666664}, {"time": 1753783500, "open": 321.02666666666664, "high": 321.02666666666664, "low": 320.8666666666667, "close": 320.8666666666667}, {"time": 1753783800, "open": 320.8666666666667, "high": 320.8666666666667, "low": 319.82, "close": 319.82}, {"time": 1753784100, "open": 319.82, "high": 319.82, "low": 318.7733333333333, "close": 318.7733333333333}, {"time": 1753784400, "open": 318.7733333333333, "high": 318.7733333333333, "low": 318.61, "close": 318.61}, {"time": 1753784700, "open": 318.61, "high": 319.33, "low": 318.61, "close": 319.33}, {"time": 1753785000, "open": 319.32, "high": 321.4, "low": 319.32, "close": 321.4}, {"time": 1753785300, "open": 321.4, "high": 321.85, "low": 321.4, "close": 321.85}, {"time": 1753785600, "open": 321.85, "high": 321.85, "low": 320.66999999999996, "close": 320.66999999999996}, {"time": 1753785900, "open": 320.66999999999996, "high": 320.66999999999996, "low": 319.48999999999995, "close": 319.48999999999995}, {"time": 1753786200, "open": 319.48999999999995, "high": 320.00333333333333, "low": 319.48999999999995, "close": 320.00333333333333}, {"time": 1753786500, "open": 320.00333333333333, "high": 322.21, "low": 320.00333333333333, "close": 322.21}, {"time": 1753786800, "open": 322.17, "high": 322.4766666666667, "low": 322.17, "close": 322.4766666666667}, {"time": 1753787100, "open": 322.4766666666667, "high": 322.4766666666667, "low": 321.92, "close": 321.92}, {"time": 1753787400, "open": 321.92, "high": 321.92, "low": 320.5, "close": 320.5}, {"time": 1753787700, "open": 320.5, "high": 320.5, "low": 319.08, "close": 319.08}, {"time": 1753788000, "open": 319.08, "high": 319.08, "low": 318.4166666666667, "close": 318.4166666666667}, {"time": 1753788300, "open": 318.4166666666667, "high": 318.51, "low": 318.4166666666667, "close": 318.51}];
	
	priceSeries.setData(priceData);

	
	volumeData = [{"time": 1753781400, "value": 1693514.1666666667, "color": "#26a69a"}, {"time": 1753781700, "value": 1693514.1666666667, "color": "#ef5350"}, {"time": 1753782000, "value": 1693514.1666666667, "color": "#ef5350"}, {"time": 1753782300, "value": 1693514.1666666667, "color": "#ef5350"}, {"time": 1753782600, "value": 1693514.1666666667, "color": "#ef5350"}, {"time": 1753782900, "value": 1693514.1666666667, "color": "#26a69a"}, {"time": 1753783200, "value": 1192203.3333333333, "color": "#26a69a"}, {"time": 1753783500, "value": 1192203.3333333333, "color": "#ef5350"}, {"time": 1753783800, "value": 1192203.3333333333, "color": "#ef5350"}, {"time": 1753784100, "value": 1192203.3333333333, "color": "#ef5350"}, {"time": 1753784400, "value": 1192203.3333333333, "color": "#ef5350"}, {"time": 1753784700, "value": 1192203.3333333333, "color": "#26a69a"}, {"time": 1753785000, "value": 1052380.1666666667, "color": "#26a69a"}, {"time": 1753785300, "value": 1052380.1666666667, "color": "#26a69a"}, {"time": 1753785600, "value": 1052380.1666666667, "color": "#ef5350"}, {"time": 1753785900, "value": 1052380.1666666667, "color": "#ef5350"}, {"time": 1753786200, "value": 1052380.1666666667, "color": "#26a69a"}, {"time": 1753786500, "value": 1052380.1666666667, "color": "#26a69a"}, {"time": 1753786800, "value": 903632.3333333334, "color": "#26a69a"}, {"time": 1753787100, "value": 903632.3333333334, "color": "#ef5350"}, {"time": 1753787400, "value": 903632.3333333334, "color": "#ef5350"}, {"time": 1753787700, "value": 903632.3333333334, "color": "#ef5350"}, {"time": 1753788000, "value": 903632.3333333334, "color": "#ef5350"}, {"time": 1753788300, "value": 903632.3333333334, "color": "#26a69a"}];
	
	volumeSeries.setData(volumeData);

	const tooltip = document.getElementById('tooltip-data');

    chart.subscribeCrosshairMove(param => {
	
      if (param && param.time){
		
		if(param.seriesData) {
			const candleData = param.seriesData.get(priceSeries);
			const volumeItem = volumeData.find(item => item.time === param.time);
			const volumeValue = volumeItem ? volumeItem.value : '--';
			if (candleData) {
				const spanClass = candleData.close >= candleData.open ? 'price-up' : 'price-down';
				tooltip.innerHTML =
				  'Open: <span class="' + spanClass + '">' + candleData.open.toFixed(2) + '</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
				  'High: <span class="' + spanClass + '">' + candleData.high.toFixed(2) + '</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
				  'Low: <span class="' + spanClass + '">' + candleData.low.toFixed(2) + '</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
				  'Close: <span class="' + spanClass + '">' + candleData.close.toFixed(2) + '</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
				  'Volume: <span class="' + spanClass + '">' + volumeValue + '</span>';
		    }
		}
	  }
     
    });
	
	function update_chart_data(data){
		priceSeries.update(data.priceData);
		volumeSeries.update(data.volumeData);
	}
	
	
	window.addEventListener('resize', () => {
		chart.resize(window.innerWidth, window.innerHeight);
	});

    </script>
  </body>
</html>