from Strategy import StrategyManager, Strategy
from Comm import *
import pandas as pd
from ibapi.order import Order
from ibapi.commission_report import CommissionReport
import Global_Values
from Logger import Logger
from pathlib import Path
from SimulateIBTool import SimulateIBTool
import json
from Strategy import Strategy_Buy_When_Break_Through
from ibapi.contract import Contract, ContractDetails

class StrategySimulator(StrategyManager):
    bar_size_to_freq = {
        '1 secs': '1s',
        '5 secs': '5s',
        '10 secs': '10s',
        '15 secs': '15s',
        '30 secs': '30s',
        '1 min': '1T',
        '2 mins': '2T',
        '3 mins': '3T',
        '5 mins': '5T',
        '10 mins': '10T',
        '15 mins': '15T',
        '20 mins': '20T',
        '30 mins': '30T',
        '1 hour': '1h',
        '2 hours': '2h',
        '3 hours': '3h',
        '4 hours': '4h',
        '8 hours': '8h',
        '1 day': '1d',
        '1 week': '1w',
        '1 month': '1m'
    }


    def scale_down_bar(self, bar: dict,
                    start_time: pd.Timestamp,
                    end_time: pd.Timestamp,
                    freq: str,
                    high_frac: float = 0.25,
                    low_frac: float = 0.75) -> pd.DataFrame:
        timeline = pd.to_datetime([
            start_time,
            start_time + (end_time - start_time) * high_frac,
            start_time + (end_time - start_time) * low_frac,
            end_time
        ])
        
        prices = [bar["open"], bar["high"], bar["low"], bar["close"]]
        curve = pd.Series(prices, index=timeline).sort_index()

        sub_index = pd.date_range(start_time, end_time, freq=freq)
    
        # interpolate curve at new points
        curve = (
            pd.Series(prices, index=timeline)
            .sort_index()
            .reindex(curve.index.union(sub_index))
            .interpolate(method="time")
            .loc[sub_index]
        )

        # build OHLC
        records = []
        nseg = len(sub_index) - 1

        for i, (t0, t1) in enumerate(zip(sub_index[:-1], sub_index[1:])):
            seg = curve[t0:t1]
            if seg.empty:
                continue

            open_ = seg.iloc[0]
            high  = seg.max()
            low   = seg.min()

            # 最后一个分段强制使用原始 Close
            # if i == nseg - 1:
            #     close = bar["close"]
            # else:
            close = seg.iloc[-1]

            records.append({
                "date":  t0,
                "open":  open_,
                "high":  high,
                "low":   low,
                "close": close,
                "volume":   bar["volume"] / nseg
                
            })

        return pd.DataFrame(records).set_index("date")


    def resample_bars(self,df: pd.DataFrame,
                    from_freq: str,
                    to_freq: str,
                    end_time: pd.Timestamp = None,
                    tradingTimeStart: str = "09:30",
                    tradingTimeEnd: str = "16:00") -> pd.DataFrame:
        df = df.copy()
        df.index = pd.to_datetime(df.index)

        if end_time is not None:
            df = df[df.index <= pd.to_datetime(end_time)]

        from_n = pd.Timedelta(from_freq)
        to_n   = pd.Timedelta(to_freq)

        # UPSAMPLE
        if to_n < from_n:
            pieces = []
            totalRows = len(df) 
            for i, (ts, row) in enumerate(df.iterrows()):
                date = ts.date()
                
                if from_freq.lower().endswith("d"):
                    bar_start = pd.Timestamp(f"{date} {tradingTimeStart}")
                    bar_end   = pd.Timestamp(f"{date} {tradingTimeEnd}")
                else:
                    
                    bar_start = ts
                    bar_end = ts + from_n
                    if i  ==  0 :
                        next_hour = ts.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                        if bar_end >= next_hour:
                            bar_end = bar_end.replace(minute=0, second=0, microsecond=0)
                            if i + 1 < totalRows:
                                if bar_end > df.index[i + 1]:
                                    bar_end = df.index[i + 1]
        
                pieces.append(self.scale_down_bar(
                    row.to_dict(), bar_start, bar_end, to_freq
                ))
                
            return pd.concat(pieces).sort_index()

        # DOWNSAMPLE
        ohlc = {
            "open": "first", "high": "max",
            "low":   "min",   "close": "last",
            "volume": "sum",  "barCount": "sum"
        }
        
        agg = df.resample(to_freq).agg(ohlc)
        return agg.drop(columns="WAPxV")


    def __init__(self, config: dict, ib: SimulateIBTool):
        super().__init__(config, ib)
        self.datasources:list[pd.DataFrame] = []
        self.mainDataSources:list[pd.DataFrame] = []
        self.ib = SimulateIBTool(config)

    def feedData(self, index: int):
        requests =  self.ib.find_request_by_type_list([REQUEST_TYPES.GET_HISTORICAL_DATA], index)     

        row = self.mainDataSources[index]
        if  requests is not None:
            for req in requests:
                barSize = req.parms["barSizeSetting"]
                freq = StrategySimulator.bar_size_to_freq[barSize]
                dataSource = self.findDataSourceByFreq(freq)
                
                if dataSource is not None:
                    df_data = dataSource[dataSource.index <= row["date"] and dataSource.index >= row["date"]]
                else:
                    df_data = self.resample_bars(self.mainDataSources[index], from_freq="5min", to_freq=freq)

                for i, row in enumerate(df_data.iterrows()):
                    self.ib.updateHistoricalData(req.reqId, df_data)
        
                
        
    def findDataSourceByFreq(self, freq: str):
        for i, row in enumerate(self.datasources):
            if row.index.freqstr == freq:
                return row


    def executePendingCommand(self, index: int):

        for order in self.ib.simulatedOrders:
            if order.orderType == "LMT":
                price = self.mainDataSources.iloc[index]["close"]
                if( price >= order.lmtPrice and order.action == "BUY" ) or (price <= order.lmtPrice and order.action == "SELL"):
                    self.ib.orderStatus(order["orderId"], "Filled",  order["totalQuantity"],  0, order["lmtPrice"], order["permId"], order["parentId"], order["lmtPrice"], order["clientId"], "", 0)
                    self.ib.execDetails(order["orderId"], order["contract"], order["execution"])
                    self.ib.execDetailsEnd(order["orderId"])

                    commissionReport = CommissionReport()
                    commissionReport.execId = order["execution"].execId
                    commissionReport.commission = self.ib.calculate_commission(order["contract"], order)
                    commissionReport.currency = order["contract"].currency
                    self.ib.commissionReport(commissionReport)

          
    def loop(self):
        
        for i, row in enumerate(self.mainDataSources):
            # feed simulated data
            self.feedData(i)
            # fill last pending order which is is not inmidily filled.
            self.executePendingCommand(i)

            # execute strategy
            for strategy in self.strategies:
                _nextWorkName = strategy.get_next_work()
                if _nextWorkName is not None :
                    
                    nextWork = getattr(strategy, _nextWorkName, None)
                    if callable(nextWork):
                        nextWork()
                        
 

if __name__ == "__main__":

    def loadJson(file:str)-> pd.DataFrame:
        path:Path = Path(f"simulated_data/{file}.json")
        with path.open("r", encoding="utf-8") as f:
            records = json.load(f) 
            df = pd.DataFrame(records)
        return df 

    Global_Values.G_IsDebug = True
    Global_Values.initConfig()
    Global_Values.G_Logger = Logger(Global_Values.G_Config, Global_Values.G_IsDebug)

    stragatgySimulator = StrategySimulator( Global_Values.G_Config, SimulateIBTool(Global_Values.G_Config))
    stragatgySimulator.mainDataSources =  loadJson("TSLA_5min")
    stragatgySimulator.datasources.append(loadJson("TSLA_1day"))
    stragatgySimulator.datasources.append(loadJson("TSLA_5sec"))


    contractDetail = ContractDetails()
   
    contract = Contract()
    contract.symbol = "TSLA"
    contract.secType = "STK"
    contract.exchange = "SMART"
    contract.currency = "USD"
    contract.primaryExchange = "NASDAQ"
    contractDetail.contract = contract

    stragatgy = Strategy_Buy_When_Break_Through(stragatgySimulator.ib, contractDetail, 0)
    stragatgy.setParms({"break_through_price": 100, "huge_volume_rate": 1.5, "check_num_of_bar_huge_volume": 10, "buy_share": 100})
    stragatgy.start()
    stragatgySimulator.add(stragatgy)   
  
    stragatgySimulator.start()
    
    



