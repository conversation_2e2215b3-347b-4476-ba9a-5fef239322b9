import sys
from PyQt6.QtWidgets import (
    QApp<PERSON>, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QTableWidget, QTableWidgetItem, QPushButton, QDialog, QLineEdit,
    QLabel, QHeaderView, QTextEdit, QFrame, QComboBox, QFormLayout, QMessageBox,QSizePolicy
)

from PyQt6.QtCore import Qt, QUrl,QEvent,QPoint, pyqtSignal, pyqtSlot, pyqtSignal,QTimer
from PyQt6.QtGui import QPainter, QColor, QMouseEvent, QKeyEvent,QTextCursor
from PyQt6.QtWebEngineWidgets import QWebEngineView  
from typing import Callable,Optional, Any
import json 
from pathlib import Path
from DataConverter import  DataConverter
from ibapi.contract import Contract
from IBTool import IBTool
import os
from functools import partial
from typing import cast

import re
from Strategy import *



class LoadScreen(QWidget):
    uiShow = pyqtSignal()
    uiHide = pyqtSignal()    
    def __init__(self, parent: QWidget):
        # 1) 父窗口下的顶级工具窗口 + 无边框 + 永远置顶
        flags = (
            Qt.WindowType.FramelessWindowHint
        )
        super().__init__(parent)

        # 2) 允许半透明绘制
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        # 3) 接收焦点以拦截键盘
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

        # 4) 在中心放一个 “Loading…” 文本
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addStretch()
        label = QLabel("Loading…", self)
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: white; font-size: 24px;")
        layout.addWidget(label)
        layout.addStretch()

        self.uiShow.connect(self._uiShow)
        self.uiHide.connect(self._uiHide)

        # 5) 初始隐藏
        self.hide()

    def _uiShow(self):
        # 先计算父窗口「客户区」在屏幕上的位置和大小
        top_left = self.parent().mapToGlobal(QPoint(0, 0))
        w = self.parent().width()
        h = self.parent().height()
        
        # 铺满客户区
        self.setGeometry(0, 0, w, h)
        self.raise_() 
        super().show()
        # 强制立刻绘制出来
        QApplication.processEvents()
        # 设置焦点以保证键盘事件进来被拦截
        self.setFocus()
        

    def _uiHide(self):
        super().hide()
        self.lower()  

    def paintEvent(self, e):
        p = QPainter(self)
        # 半透明灰色遮罩
        p.fillRect(self.rect(), QColor(0, 0, 0, 128))


#========================================================================
# BlockerFrame：遮罩層，攔截所有滑鼠與鍵盤事件
class BlockerFrame(QFrame):
    def __init__(self, parent=None):
        super().__init__(parent)
    def mousePressEvent(self, event):
        event.accept()
    def mouseReleaseEvent(self, event):
        event.accept()
    def mouseDoubleClickEvent(self, event):
        event.accept()

#========================================================================
# 搜尋金融產品的表單（保持不變）

SEC_TYPE_LIST = {'STK': 'Stock', 'OPT': 'Option', 'FUT': 'Future', 'IND': 'Index', 'FOP': 'Futures Option', 'CASH': 'Forex Pair BAG', 'WAR': 'Warrant', 'BOND': 'Bond', 'CMDTY': 'Commodity', 'NEWS': 'News', 'FUND': 'Mutual fund'}

class SearchForm(QDialog):
    def __init__(self, mode='add', row_data:  Optional[dict]=None, parent = None, 
                 Handle_On_Load: Optional[Callable[['SearchForm'], None]] = None, 
                 Handle_OK_OnClicked: Optional[Callable[['SearchForm'], None]] = lambda form: None
                 ):
        super(SearchForm, self).__init__(parent)
        self.setWindowTitle("搜尋金融產品")
        self.mode = mode
        self.row_data = row_data

        self.Handle_On_Load = Handle_On_Load
        self.Handle_OK_OnClicked = Handle_OK_OnClicked
        self.parent_QWidget = parent

        self.initUI()

        if self.Handle_On_Load is not None:
            self.Handle_On_Load(self)
 

    def initUI(self):
        layout = QVBoxLayout()
        self.symbolEdit = QLineEdit()
        self.secTypeComboEdit = QComboBox()
        
        for key, value in SEC_TYPE_LIST.items():
            self.secTypeComboEdit.addItem(value, key)

        # self.exchangeEdit = QLineEdit()
        # self.exchangeEdit.setText("SMART")
        # self.currencyEdit = QLineEdit()
        # self.currencyEdit.setText("USD")
        # self.primaryExchangeEdit = QLineEdit()
        # self.primaryExchangeEdit.setText("NASDAQ")

        exchange_codes = ["SMART","NASDAQ","NYSE","TSEJ","ARCA","AMEX","BATS","BYX","EDGA","EDGX","IEX","INET"]

        self.exchangeEdit = QComboBox()
        for code in exchange_codes:
            self.exchangeEdit.addItem(code)

        currency_codes = ["USD","JPY","EUR","GBP","AUD","CAD","CHF","NZD","SEK","NOK","KRW","CNY","HKD","SGD","MXN","INR","RUB","ZAR","TRY","AED","SAR","QAR","KWD","OMR","JOD","BHD","ILS","EGP","COP","CLP","ARS","PEN","BRL"]

        self.currencyEdit = QComboBox()
        for code in currency_codes:
            self.currencyEdit.addItem(code)


        primaryExchange_codes = ["SMART","NASDAQ","NYSE","TSEJ","ARCA","AMEX","BATS","BYX","EDGA","EDGX","IEX","INET"]
        self.primaryExchangeEdit = QComboBox()
        for code in primaryExchange_codes:
            self.primaryExchangeEdit.addItem(code)
        


        layout.addWidget(QLabel("Symbol:"))
        layout.addWidget(self.symbolEdit)
        layout.addWidget(QLabel("secType:"))
        layout.addWidget(self.secTypeComboEdit)
        layout.addWidget(QLabel("Exchange:"))
        layout.addWidget(self.exchangeEdit)
        layout.addWidget(QLabel("Currency:"))
        layout.addWidget(self.currencyEdit)
        layout.addWidget(QLabel("Primary Exchange:"))
        layout.addWidget(self.primaryExchangeEdit)


        btnLayout = QHBoxLayout()
        self.okButton = QPushButton("OK")
        self.cancelButton = QPushButton("Cancel")
        btnLayout.addWidget(self.okButton)
        btnLayout.addWidget(self.cancelButton)
        layout.addLayout(btnLayout)
        self.setLayout(layout)

        if self.Handle_OK_OnClicked is not None:
            self.okButton.clicked.connect(lambda _, : self.Handle_OK_OnClicked(self))
      
        self.cancelButton.clicked.connect(self.reject)

     
      
    def Cancel_clicked(self):
        self.reject()

    def getData(self)-> Optional[dict]:
        return self.row_data





#========================================================================
# 策略表單：用於新增/編輯策略的表單
class StrategyForm(QDialog):
    def __init__(self, mode="add", strategy=None, contractDetail: Optional[ContractDetails] = None, parent=None, 
                 Handle_On_Load: Optional[Callable[['StrategyForm'], None]] = None, 
                 Handle_OK_OnClicked: Optional[Callable[['StrategyForm'], None]] = lambda form: None
                 ):
       
        super().__init__(parent)
        self.setWindowTitle("策略表單")
        self.Handle_On_Load = Handle_On_Load
        self.Handle_OK_OnClicked = Handle_OK_OnClicked

        self.mode = mode
        self.strategy = strategy
        self.contractDetail = contractDetail

        self.detailForm = None
        

        self.initUI()
        if self.Handle_On_Load is not None:
            self.Handle_On_Load(self)

    
    def initUI(self):
        self.fromLayout = QVBoxLayout(self)
     
        # Main Panel – 策略類型的選取
        self.comboStrategy = QComboBox(self)
        self.strategy_types = [] #["Break through buy strategy", "Default strategy"]
        for strategyForm in STRATEGY_FORM_LIST:
            self.strategy_types.append(strategyForm["name"])

        self.comboStrategy.addItems(self.strategy_types)
     

        self.fromLayout.addWidget(QLabel("策略類型："))
        self.fromLayout.addWidget(self.comboStrategy)
        

        self.detailFormPanel = QWidget(self)
        self.detailFormPanel.setContentsMargins(0, 0, 0, 0)
        self.detailLayout = QVBoxLayout(self.detailFormPanel)
        self.detailLayout.setContentsMargins(0, 0, 0, 0)
        self.detailFormPanel.setLayout(self.detailLayout)
        self.fromLayout.addWidget(self.detailFormPanel)


        self.populateDetailPanel()
        self.comboStrategy.currentIndexChanged.connect(self.populateDetailPanel)
        
        # Button Panel – OK 與 Close
        btnPanel = QHBoxLayout()
        self.okButton = QPushButton("OK", self)
        self.closeButton = QPushButton("Close", self)
        btnPanel.addWidget(self.okButton)
        btnPanel.addWidget(self.closeButton)

        self.fromLayout.addLayout(btnPanel)
        
        if self.Handle_OK_OnClicked is not None:
            self.okButton.clicked.connect(lambda _,   form=self : self.Handle_OK_OnClicked(self))
     
        self.closeButton.clicked.connect(self.reject)
    
    def populateDetailPanel(self):
        idx =  self.comboStrategy.currentIndex()
        if  idx == -1:
            return

        if self.detailForm is not None:
            self.detailLayout.removeWidget(self.detailForm)
            self.detailForm.deleteLater()
            self.detailForm = None

        form_cls = STRATEGY_FORM_LIST[idx]["form"]
        self.detailForm = form_cls(self.detailFormPanel, self.strategy, self.contractDetail)

        self.detailLayout.addWidget(self.detailForm)



class StrategyForm_Buy_When_Break_Through(QWidget):
    def __init__(self, parent, strategy:Strategy, contractDetail:ContractDetails):
        super().__init__(parent)
        self._pattern_float = r'^[0-9]+(\.[0-9]{1,2})?$'
        self._pattern_int = r'^[0-9]+$'
        self.strategy = strategy
        self.contractDetail = contractDetail
        # if self.contractDetail is None:
        #     if self.strategy is not None:
        #         self.contractDetail = self.strategy.contractDetail

        self.initUI()
        self.On_Load()
    
    def initUI(self):
        self.detailLayout = QFormLayout(self)
        
        self.break_through_price_edit = QLineEdit()
        self.break_through_price_edit.setText("1")
        self.detailLayout.addRow(QLabel("Break through price:"), self.break_through_price_edit)

        self.huge_volume_rate_edit = QLineEdit()
        self.huge_volume_rate_edit.setText("1.5")
        self.detailLayout.addRow(QLabel("Huge volume rate:"), self.huge_volume_rate_edit)

        self.check_num_of_bar_huge_volume_edit = QLineEdit()
        self.check_num_of_bar_huge_volume_edit.setText("20")
        self.detailLayout.addRow(QLabel("Check num of bar huge volume:"), self.check_num_of_bar_huge_volume_edit)

        self.buy_share_edit = QLineEdit()
        self.buy_share_edit.setText("1")
        self.detailLayout.addRow(QLabel("Buy share:"), self.buy_share_edit)


    def On_Load(self):
        if  self.strategy is not None:
            self.break_through_price_edit.setText(str(self.strategy.data["break_through_price"]))
            self.huge_volume_rate_edit.setText(str(self.strategy.data["huge_volume_rate"]))
            self.check_num_of_bar_huge_volume_edit.setText(str(self.strategy.data["check_num_of_bar_huge_volume"]))
            self.buy_share_edit.setText(str(self.strategy.data["buy_share"]))


    def checkData(self):
        


        errMsg = ""

        price = float(self.break_through_price_edit.text().strip())
        if  OrderVaildator.roundPrice(price, self.contractDetail.minTick) != price:
            errMsg += f"Break through price is not a valid price. the minTick is { self.contractDetail.minTick }"

        if not re.fullmatch(self._pattern_float, self.huge_volume_rate_edit.text().strip()):
            errMsg += "Huge volume rate is a float"

        if not re.fullmatch(self._pattern_int, self.check_num_of_bar_huge_volume_edit.text().strip()):
            errMsg += "Check num of bar huge volume is a int"

        check_num_of_bar_huge_volume = int (self.check_num_of_bar_huge_volume_edit.text())
        if check_num_of_bar_huge_volume < 10:
            errMsg += "Check num of bar huge volume is less than 10"

        buy_share = Decimal(self.buy_share_edit.text().strip())
        if  OrderVaildator.roundQuantity(buy_share, self.contractDetail.minSize, self.contractDetail.sizeIncrement) != buy_share:
            errMsg += f"Buy share is not a valid quantity. the minSize is { self.contractDetail.minSize } and the sizeIncrement is { self.contractDetail.sizeIncrement }"

        if errMsg != "":
            
            return errMsg
        else:
            return None
       


    def getData(self)-> dict:
        return {
            "break_through_price": float (self.break_through_price_edit.text().strip()),
            "huge_volume_rate": float( self.huge_volume_rate_edit.text().strip()),
            "check_num_of_bar_huge_volume": int(self.check_num_of_bar_huge_volume_edit.text().strip()),
            "buy_share": int(self.buy_share_edit.text().strip())
        }


        
STRATEGY_FORM_LIST = [
    {"name": "BUY WHEN BREAK THROUGH", "class": Strategy_Buy_When_Break_Through, "form": StrategyForm_Buy_When_Break_Through}
]

#========================================================================
# ChartWidget：保持原有圖表功能
class ChartWidget(QWidget):
    updateBarJS = pyqtSignal(str)
    def __init__(self, chart_name="Chart", parent=None,
                 Handle_OnUpdateVisibility: Optional[Callable[['ChartWidget'], None]] = None,
                 Handle_OnChartViewReady: Optional[Callable[['ChartWidget'], None]] = None
                 ):
        super(ChartWidget, self).__init__(parent)
        self.chart_name = chart_name
        self.parent_QWidget = parent
        # self.visible_flag = True if chart_name == "Daily Chart" else False
        self.visible_flag = False
        self.zoomed = False  
        self.req = None
        self.pendingJS: list[str] = []
        self.isChartViewReady = False
        self.Handle_OnChartViewReady = Handle_OnChartViewReady
        self.Handle_OnUpdateVisibility = Handle_OnUpdateVisibility
        self.initUI()

        self.updateBarJS.connect(self._on_update_bar_js)
        
    
    def initUI(self):
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.chartView = QWebEngineView(self)
        self.chartView.loadFinished.connect(lambda _, chartWidget=self: self.Handle_OnChartViewReady(chartWidget))        

        self.layout.addWidget(self.chartView)
        self.cover = BlockerFrame(self)
        self.cover.setStyleSheet("background-color: rgba(128, 128, 128, 0.7);")
        self.showButton = QPushButton("", self)
        self.showButton.setStyleSheet("background-color: lightgrey;")
        self.showButton.resize(50, 30)
        self.showButton.clicked.connect(self.toggleVisibility)
 
        
        self.zoomButton = QPushButton("放大", self)
        self.zoomButton.setStyleSheet("background-color: lightgrey;")
        self.zoomButton.resize(60, 30)
        self.zoomButton.clicked.connect(self.toggleZoom)
        
        self.updateVisibility()

        self.chartView.setVisible(True)

        
        
    @pyqtSlot(str)
    def _on_update_bar_js(self, js: str):
        self.chartView.page().runJavaScript(js)

    

    def resizeEvent(self, event):
        self.cover.setGeometry(0, 0, self.width(), self.height())
        self.showButton.move(self.width() - self.showButton.width(), 0)
        self.zoomButton.move(self.width() - self.showButton.width() - self.zoomButton.width() - 5, 0)
        
        if self.cover.isVisible():  
            self.cover.raise_()

        self.showButton.raise_()    
        super().resizeEvent(event)
    
    def updateVisibility(self):
        
        if self.visible_flag:
            self.chartView.setVisible(True)
            self.cover.hide()
            self.showButton.setText("隱")
        else:
            self.chartView.setVisible(False)
            self.cover.show()
            self.cover.raise_()
            self.showButton.setText("顯")
            self.showButton.raise_()

        if self.Handle_OnUpdateVisibility is not None:
            self.parent_QWidget.loadingScreen.uiShow.emit()
            self.Handle_OnUpdateVisibility(self)
            self.parent_QWidget.loadingScreen.uiHide.emit()
    
    def toggleVisibility(self):
        mainWindow = cast(MainWindow, self.parent_QWidget)
        row = mainWindow.watchList.currentRow()
        if row == -1 or row == mainWindow.watchList.rowCount()-1:
            return

        # 如果當前圖表正顯示，且正處於 zoom in 狀態，則在隱藏前做 zoom out
        if self.visible_flag:
            if self.zoomed:
                mainWindow = self.window()  # 取得最上層 MainWindow
                mainWindow.unzoomChart(self)
                self.zoomed = False
                self.zoomButton.setText("放大")
            self.visible_flag = False
        else:
            self.visible_flag = True
        self.updateVisibility()
        
    
    def toggleZoom(self):
        if not self.cover.isVisible():
            mainWindow = self.window()
            if not self.zoomed:
                self.zoomed = True
                mainWindow.zoomChart(self)
                self.zoomButton.setText("縮小")
            else:
                self.zoomed = False
                mainWindow.unzoomChart(self)
                self.zoomButton.setText("放大")


#========================================================================
# MainWindow：主視窗，包含 Watch List、Strategies、Charts 與 Message Session
class MainWindow(QWidget):
    mainThreadAppendMessage = pyqtSignal(str)

    def __init__(self, config:dict, 
                Handle_OnLoad: Callable,
                MainWindow_OnClose: Callable,
                Handle_WatchList_RowAdd: Callable,
                Handle_WatchList_RowEdit: Callable,
                Handle_WatchList_RowDelete: Callable,
                Handle_WatchList_RowSelected: Callable,
                Handle_Chart_OnUpdateVisibility: Callable,
                Handle_Chart_OnChartViewReady: Callable,
                Handle_StrategyList_RowAdd: Callable,
                Handle_StrategyList_RowEdit: Callable,
                Handle_StrategyList_RowDelete: Callable,
                Handle_StrategyList_RowStart: Callable,
                Handle_StrategyList_RowPause: Callable,
                Handle_StrategyList_RowStop: Callable
                ):
        

        super(MainWindow, self).__init__()
        self.setWindowTitle("金融產品策略 UI")
        self.setGeometry(100, 100, 2560, 1440)
        self.message_count = 0
        self.zoomedChart = None  # 當前放大的圖表
        # 策略資料以列表存放，每筆為字典
        # self.strategies = []  
        self.config = config
       
        self.Handle_OnLoad = Handle_OnLoad
        self.MainWindow_OnClose = MainWindow_OnClose
        self.Handle_WatchList_RowAdd = Handle_WatchList_RowAdd
        self.Handle_WatchList_RowEdit = Handle_WatchList_RowEdit
        self.Handle_WatchList_RowDelete = Handle_WatchList_RowDelete
        self.Handle_WatchList_RowSelected = Handle_WatchList_RowSelected
        self.Handle_Chart_OnUpdateVisibility = Handle_Chart_OnUpdateVisibility
        self.Handle_Chart_OnChartViewReady = Handle_Chart_OnChartViewReady
        self.Handle_StrategyList_RowAdd = Handle_StrategyList_RowAdd
        self.Handle_StrategyList_RowEdit = Handle_StrategyList_RowEdit
        self.Handle_StrategyList_RowDelete = Handle_StrategyList_RowDelete
        self.Handle_StrategyList_RowStart = Handle_StrategyList_RowStart
        self.Handle_StrategyList_RowPause = Handle_StrategyList_RowPause
        self.Handle_StrategyList_RowStop = Handle_StrategyList_RowStop
  

        self.initUI()
        
        self.mainThreadAppendMessage.connect(self.appendMessage)

        if self.Handle_OnLoad is not None:
            QTimer.singleShot(0, lambda: self.Handle_OnLoad(self))
            

        
    def initUI(self):
        mainLayout = QHBoxLayout(self)
        self.setLayout(mainLayout)
        self.loadingScreen = LoadScreen(self)
        # ---------- 左側面板（Watch List 與 Strategies） ----------
        leftPanel = QWidget(self)
        leftLayout = QVBoxLayout(leftPanel)
        leftLayout.setContentsMargins(2, 2, 2, 2)
        mainLayout.addWidget(leftPanel, 1)
        
        # Watch List 區域（上方，佔 2/5）
        self.watchList = QTableWidget(0, 6, self)
        self.watchList.setStyleSheet("""
            QHeaderView::section {
                font-weight: normal;
            }
        """)
        self.watchList.setHorizontalHeaderLabels(["Symbol", "Bid", "Ask", "Last",  "編輯", "刪除"])
        self.watchList.verticalHeader().setVisible(False)
        self.watchList.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.watchList.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.watchList.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.watchList.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        if self.Handle_WatchList_RowSelected:
            self.watchList.cellClicked.connect(lambda  row, col: self.Handle_WatchList_RowSelected(self, row, col))

        self.watchListAddAddButtonRow()
        watchListFrame = QFrame()
        watchListLayout = QVBoxLayout(watchListFrame)
        watchListLayout.addWidget(self.watchList)
        leftLayout.addWidget(watchListFrame, 2)
        
        # Strategies 區域（下方，佔 3/5）
        # 5 欄：策略名稱、狀態、操作、編輯、刪除
        self.strategyList = QTableWidget(0, 5, self)
        self.strategyList.setHorizontalHeaderLabels(["策略名稱", "狀態", "操作", "編輯", "刪除"])
        self.strategyList.verticalHeader().setVisible(False)
        self.strategyList.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.strategyList.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.drawStrategyList([])
        strategyFrame = QFrame()
        strategyLayout = QVBoxLayout(strategyFrame)
        strategyLayout.addWidget(self.strategyList)
        leftLayout.addWidget(strategyFrame, 3)
        
        # ---------- 右側面板（Charts Session 與 Message Session） ----------
        rightPanel = QWidget(self)
        rightLayout = QVBoxLayout(rightPanel)
        rightLayout.setContentsMargins(2, 2, 2, 0)
        mainLayout.addWidget(rightPanel, 4)
        
        # Charts Session：右上方，佔 4/5
        self.chartsFrame = QFrame()
        self.chartsLayout = QGridLayout(self.chartsFrame)
        self.chartsLayout.setSpacing(5)
        self.dailyChart = ChartWidget("Daily Chart", self, self.Handle_Chart_OnUpdateVisibility, self.Handle_Chart_OnChartViewReady)
        self.weeklyChart = ChartWidget("Weekly Chart", self, self.Handle_Chart_OnUpdateVisibility, self.Handle_Chart_OnChartViewReady)
        self.fiveMinsChart = ChartWidget("5 mins Chart", self, self.Handle_Chart_OnUpdateVisibility, self.Handle_Chart_OnChartViewReady)
        self.oneSecChart = ChartWidget("1 sec Chart", self, self.Handle_Chart_OnUpdateVisibility, self.Handle_Chart_OnChartViewReady)
        self.charts = [self.dailyChart, self.weeklyChart, self.fiveMinsChart, self.oneSecChart]
        self.chartsLayout.addWidget(self.dailyChart, 0, 0)
        self.chartsLayout.addWidget(self.weeklyChart, 0, 1)
        self.chartsLayout.addWidget(self.fiveMinsChart, 1, 0)
        self.chartsLayout.addWidget(self.oneSecChart, 1, 1)
        rightLayout.addWidget(self.chartsFrame, 4)
        
        # Message Session：右下方，佔 1/5
        self.messageSession = QTextEdit(self)
        self.messageSession.setReadOnly(True)
        self.messageSession.setLineWrapMode(QTextEdit.LineWrapMode.WidgetWidth)
        self.messageSession.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.messageSession.setStyleSheet("QTextEdit { border: none; }")
        rightLayout.addWidget(self.messageSession, 1)

        
        # mainLayout.addWidget(self.loadingScreen)


    def closeEvent(self, event):
        if self.MainWindow_OnClose is not None:
            self.MainWindow_OnClose(self)
        super().closeEvent(event)
    


    #---------- Watch List 相關（文字項目設定為只讀）----------
    def watchListAddAddButtonRow(self):
        row = self.watchList.rowCount()
        self.watchList.insertRow(row)
        addButton = QPushButton("Add")
        if self.Handle_WatchList_RowAdd:
            addButton.clicked.connect(lambda _, r=row: self.Handle_WatchList_RowAdd(self, mode="add", row_index=r))

        self.watchList.setCellWidget(row, 0, addButton)
        for col in range(1, 6):
            item = QTableWidgetItem("")
            item.setFlags(Qt.ItemFlag.ItemIsSelectable | Qt.ItemFlag.ItemIsEnabled)
            self.watchList.setItem(row, col, item)
        
            
    def watchListAddRow(self, row:dict):
        idx = self.watchList.rowCount()-1
        self.watchList.insertRow(idx)
        self._watchListAddRow(row, idx)

    def _watchListAddRow(self, row: dict, row_index):
        contractDetail:ContractDetails =  row["contractDetail"]
        contract = contractDetail.contract
        for i, key in enumerate(["symbol", "Bid", "Ask", "Last"]):
            item = QTableWidgetItem(contract.symbol if i == 0 else "")
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            if  i ==  0:
                item.setData(Qt.ItemDataRole.UserRole, row)
            item.setFlags(Qt.ItemFlag.ItemIsSelectable | Qt.ItemFlag.ItemIsEnabled)
            self.watchList.setItem(row_index, i, item)

        editBtn = QPushButton("Edit")
        if self.Handle_WatchList_RowEdit:
            editBtn.clicked.connect(lambda _, r=row_index: self.Handle_WatchList_RowEdit(self, mode="edit", row_index=r))

        self.watchList.setCellWidget(row_index, 4, editBtn)
        delBtn = QPushButton("Delete")
        if self.Handle_WatchList_RowDelete:
            delBtn.clicked.connect(lambda _, r=row_index: self.Handle_WatchList_RowDelete(self,r))
     
        self.watchList.setCellWidget(row_index, 5, delBtn)     


      
    
    def drawStrategyList(self, strategies:list[Strategy]):
        self.strategyList.setRowCount(0)
        for i, strat in enumerate(strategies):
            row = self.strategyList.rowCount()
            self.strategyList.insertRow(row)
            
            # 策略名稱、狀態（設定為只讀）


            for item in STRATEGY_FORM_LIST:
                if item["class"] == strat.__class__:
                    name = item["name"]
                    break

            name_item = QTableWidgetItem(name)
            name_item.setFlags(Qt.ItemFlag.ItemIsSelectable | Qt.ItemFlag.ItemIsEnabled)
            name_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.strategyList.setItem(row, 0, name_item)
            name_item.setData(Qt.ItemDataRole.UserRole, strat)
            
            status_item = QTableWidgetItem(strat.state.name)
            status_item.setFlags(Qt.ItemFlag.ItemIsSelectable | Qt.ItemFlag.ItemIsEnabled)
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.strategyList.setItem(row, 1, status_item)
            
            # 操作欄：根據狀態決定按鈕組合
            opWidget = QWidget()
            opLayout = QHBoxLayout(opWidget)
            opLayout.setContentsMargins(0, 0, 0, 0)
            opLayout.setSpacing(0)
  
            # if stra["status"] in ["未開始", "暫停中"]:
                # 顯示 Start 與 Stop 按鈕
            btnStart = QPushButton("Start")
            btnStart.setMaximumWidth(45)
            if self.Handle_StrategyList_RowStart:
                btnStart.clicked.connect(lambda checked, index=i: self.Handle_StrategyList_RowStart(self, index, strat))
            # btnStart.clicked.connect(lambda checked, index=i: self.toggleStrategyOp(index))
            btnPause = QPushButton("Pause")
            btnPause.setMaximumWidth(45)
            if self.Handle_StrategyList_RowPause:
                btnPause.clicked.connect(lambda checked, index=i: self.Handle_StrategyList_RowPause(self, index, strat))
            # btnPause.clicked.connect(lambda checked, index=i: self.toggleStrategyOp(index))
            btnStop = QPushButton("Stop")
            btnStop.setMaximumWidth(45)

            if self.Handle_StrategyList_RowStop:
                btnStop.clicked.connect(lambda checked, index=i: self.Handle_StrategyList_RowStop(self, index, strat))
            # btnStop.clicked.connect(lambda checked, index=i: self.stopStrategy(index))
            opLayout.addWidget(btnStart)
            opLayout.addWidget(btnPause)
            opLayout.addWidget(btnStop)
            # elif strat["status"] == "執行中":

            if strat.state == Strategy.STRATEGY_EXECUTE_STATE.STOPPED:
                btnStart.setVisible(True)
                btnPause.setVisible(False)
                btnStop.setVisible(False)
            elif strat.state == Strategy.STRATEGY_EXECUTE_STATE.RUNNING:
                btnStart.setVisible(False)
                btnPause.setVisible(True)
                btnStop.setVisible(True)
            elif strat.state == Strategy.STRATEGY_EXECUTE_STATE.PAUSED:
                btnStart.setVisible(True)
                btnPause.setVisible(False)
                btnStop.setVisible(True)

                # 顯示 Pause 與 Stop 按鈕
        
            self.strategyList.setCellWidget(row, 2, opWidget)
            
            # 編輯按鈕
            editBtn = QPushButton("Edit")
            editBtn.setMaximumWidth(45)
          
            if self.Handle_StrategyList_RowEdit:
                editBtn.clicked.connect(lambda checked, index=i: self.Handle_StrategyList_RowEdit(self, mode="edit", index=index, strategy=strat))
            # editBtn.clicked.connect(lambda checked, index=i: self.openStrategyForm(mode="edit", index=index))
            self.strategyList.setCellWidget(row, 3, editBtn)
            
            # 刪除按鈕
            delBtn = QPushButton("DEL")
            delBtn.setMaximumWidth(40)
            if self.Handle_StrategyList_RowDelete:
                delBtn.clicked.connect(lambda checked, index=i: self.Handle_StrategyList_RowDelete(self, index, strat))
            # delBtn.clicked.connect(lambda checked, index=i: self.deleteStrategy(index))
            self.strategyList.setCellWidget(row, 4, delBtn)

            header = self.strategyList.horizontalHeader()   # ← 取出水平表頭
            header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
            for col in (1, 2, 3, 4):
                header.setSectionResizeMode(col, QHeaderView.ResizeMode.ResizeToContents)

        # 新增最後一行作為「Add Strategy」按鈕，僅在策略名稱那一欄顯示
        row = self.strategyList.rowCount()
        self.strategyList.insertRow(row)
        addStratBtn = QPushButton("Add Strategy")
        if self.Handle_StrategyList_RowAdd:
            addStratBtn.clicked.connect(lambda _, r=row: self.Handle_StrategyList_RowAdd(self, mode="add", index=r))
        # addStratBtn.clicked.connect(lambda: self.openStrategyForm(mode="add"))
        self.strategyList.setCellWidget(row, 0, addStratBtn)


    
    
    def appendMessage(self, message):
        self.message_count += 1
        self.messageSession.append(message)
        if self.message_count > 1000:
            lines = self.messageSession.toPlainText().split('\n')
            if len(lines) > 1000:
                lines = lines[-1000:]
                self.messageSession.setPlainText("\n".join(lines))
                self.message_count = len(lines)
        
        
        cursor = self.messageSession.textCursor()

        # move the cursor to the very end of the document
        cursor.movePosition(
            QTextCursor.MoveOperation.End,
            QTextCursor.MoveMode.MoveAnchor
        )

        # re-apply it to the widget and make sure it’s visible
        self.messageSession.setTextCursor(cursor)
        self.messageSession.ensureCursorVisible()
    
    #---------- Zoom 與 Unzoom 相關（保持原有）----------
    def zoomChart(self, chart_widget):
        if self.zoomedChart and self.zoomedChart != chart_widget:
            self.unzoomChart(self.zoomedChart)
        self.zoomedChart = chart_widget
        for chart in self.charts:
            if chart != chart_widget:
                chart.hide()
                
        self.chartsLayout.removeWidget(chart_widget)
        self.chartsLayout.addWidget(chart_widget, 0, 0, 2, 2)
    
    def unzoomChart(self, chart_widget):
        self.zoomedChart = None
        self.chartsLayout.removeWidget(chart_widget)
        self.chartsLayout.addWidget(self.dailyChart, 0, 0)
        self.chartsLayout.addWidget(self.weeklyChart, 0, 1)
        self.chartsLayout.addWidget(self.fiveMinsChart, 1, 0)
        self.chartsLayout.addWidget(self.oneSecChart, 1, 1)
        for chart in self.charts:
            chart.show()

       



