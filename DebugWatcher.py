import sys
import traceback, os, sysconfig, threading


    
def monitor(target, depth=5):
    """
    对 类 或 实例 进行全局跟踪：
      • CALL：只要调用 target 或其实例的方法（含 __init__），打印最多 depth 层栈
      • RETURN：只要函数/方法返回 target 或其实例，也打印栈
    """
    is_class = isinstance(target, type)
    if is_class:
        target_cls  = target
        monitor_ids = set()       # 用于记录所有被创建的实例 id
        desc        = f"类<{target.__name__}>"
    else:
        monitor_ids = {id(target)}
        desc        = f"实例<{target.__class__.__name__}"

    def _print_stack(kind):
        stk = traceback.extract_stack()[:-2]
        user = [(f,l,fn,cd) for f,l,fn,cd in stk if _is_user_file(f)]
        frames = (user or stk)[-depth:]
        print(f"\n====== {desc} 的[{kind}]触发（最多 {depth} 层）")
        for i,(f,l,fn,cd) in enumerate(reversed(frames), 1):
            b = os.path.basename(f)
            if i == 1:
                print(f"  {b}:{l} in {fn}")
            else:
                print(f"  {b}:{l} in {fn} -> {cd.strip()}")
        print("="*50)
        
    # —— 过滤系统库 & 第三方包 ——#
    STD_LIB   = sysconfig.get_paths()['stdlib']
    SITE_PKGS = [p for p in sys.path if "site-packages" in p]
    def _is_user_file(fn: str) -> bool:
        if fn.startswith("<"): return False
        n = os.path.normcase(fn)
        if n.startswith(os.path.normcase(STD_LIB)):    return False
        if any(n.startswith(os.path.normcase(sp)) for sp in SITE_PKGS): return False
        return True

    def _tracer(frame, event, arg):
        fn = frame.f_code.co_filename
        
        if not _is_user_file(fn)  :
            return _tracer
    
        name = frame.f_code.co_name
        if event == "call":
            self_obj = frame.f_locals.get("self")

            if is_class and isinstance(self_obj, target_cls):
                _print_stack("CALL")

            # 只在被监控对象就是当前 self 时打印栈
            elif self_obj is not None and id(self_obj) in monitor_ids:
                _print_stack("CALL")
  

        elif event == "return":
            # 只要返回值是被监控对象，就触发 RETURN
            if id(arg) in monitor_ids:
                _print_stack("RETURN")

       
        return _tracer

    sys.settrace(_tracer)
    threading.settrace(_tracer)


# ==== 使用示例 ==== #
if __name__ == "__main__":
    class CCC:
        def __init__(self, z): self.z = z
        def xxx(self): print("xxxx")

    class Bar:
        def __init__(self, y):
            self.y   = y
            self.ccc = CCC(3)
        def do(self):
            print("do")
            self.ccc.xxx()
        def ddd(self):
            self.y   = 213
            self.ccc.xxx()


 
    monitor(Bar, depth=5)
    bar = Bar(1)
    bar.ddd()



