<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0"
    />
    <title>Lightweight Charts™ Customization Tutorial</title>
    <!-- Adding the standalone version of Lightweight charts -->
    <!--
	<script
      type="text/javascript"
      src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"
    ></script>
	-->
	
	<script
		type="text/javascript"
		src="lightweight-charts5.07.js">
	</script>
    <style>
      body {
        padding: 0;
        margin: 0;
        /* Add a background color to match the chart */
        background-color: #222;
      }
	  
	  #tooltip {
		  position: absolute;
		  top: 10px;
		  left: 10px;
		  z-index: 100;
		  background-color: rgba(0, 0, 0, 0);
		  padding: 4px 6px;
		  border-radius: 4px;
		  font-size: 14px;
		  pointer-events: none;
		  color: #DDD !important;
	 } 
	 
	 .price-up {
        color: #26a69a;
        font-family: Arial, sans-serif;
        font-size: 14px;
     }
      
     .price-down {
        color: #ef5350;
        font-family: Arial, sans-serif;
        font-size: 14px;
     }		 
    </style>
  </head>

  <body>
    <div id="container" style="position: absolute; width: 100%; height: 100%">    
		<div id="tooltip">
			<span id="symbol"><!-- SYMBOL_PLACEHOLDER --></span>&nbsp;&nbsp;&nbsp;&nbsp;
			 <span id="tooltip-data">Open: --      High: --      Low: --      Close: --      Volume: -- </span>     
		</div>
	</div>
    <script type="text/javascript">
	

	/** @type {import('lightweight-charts').IChartApi} */

	const chart = LightweightCharts.createChart(
		document.getElementById('container'),
		{
			layout: {
				background: { color: '#222' },
				textColor: '#DDD',
			},
			grid: {
				vertLines: { color: '#444' },
				horzLines: { color: '#444' },
			},
			crosshair: {
				mode: LightweightCharts.CrosshairMode.Normal,
			},
			timeScale: {
				timeVisible:    true,  // 顯示時分
				secondsVisible: true   // 顯示秒
			},
		}
	);

	const priceSeries = chart.addSeries(LightweightCharts.CandlestickSeries);

	priceSeries.priceScale().applyOptions({
		scaleMargins: {
			top: 0.1, // highest point of the series will be 70% away from the top
			bottom: 0.2,
		},
		
		//borderColor: '#888',
		//barSpacing: 2,
		
	});

	const volumeSeries = chart.addSeries(LightweightCharts.HistogramSeries, {
		color: '#26a69a',
		priceFormat: {
			type: 'volume',
		},
		priceScaleId: '', // set as an overlay by setting a blank priceScaleId

	});

	volumeSeries.priceScale().applyOptions({
		scaleMargins: {
			top: 0.85, // highest point of the series will be 70% away from the top
			bottom: 0,
		},
	});


	var priceData= [{"time": 1753781400, "open": 325.63, "high": 325.8207692307692, "low": 325.63, "close": 325.8207692307692}, {"time": 1753781400, "open": 321.8, "high": 322.44307692307694, "low": 321.8, "close": 322.44307692307694}, {"time": 1753781400, "open": 322.17, "high": 322.3115384615385, "low": 322.17, "close": 322.3115384615385}];
	priceSeries.setData(priceData);

	
	volumeData = [{"time": 1753781400, "value": 781621.9230769231, "color": "#26a69a"}, {"time": 1753781400, "value": 610692.5384615385, "color": "#26a69a"}, {"time": 1753781400, "value": 858593.2307692308, "color": "#26a69a"}];
	
	volumeSeries.setData(volumeData);

	const tooltip = document.getElementById('tooltip-data');

    chart.subscribeCrosshairMove(param => {
	
      if (param && param.time){
		
		if(param.seriesData) {
			const candleData = param.seriesData.get(priceSeries);
			const volumeItem = volumeData.find(item => item.time === param.time);
			const volumeValue = volumeItem ? volumeItem.value : '--';
			if (candleData) {
				const spanClass = candleData.close >= candleData.open ? 'price-up' : 'price-down';
				tooltip.innerHTML =
				  'Open: <span class="' + spanClass + '">' + candleData.open.toFixed(2) + '</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
				  'High: <span class="' + spanClass + '">' + candleData.high.toFixed(2) + '</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
				  'Low: <span class="' + spanClass + '">' + candleData.low.toFixed(2) + '</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
				  'Close: <span class="' + spanClass + '">' + candleData.close.toFixed(2) + '</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
				  'Volume: <span class="' + spanClass + '">' + volumeValue + '</span>';
		    }
		}
	  }
     
    });
	
	function update_chart_data(data){
		priceSeries.update(data.priceData);
		volumeSeries.update(data.volumeData);
	}
	
	
	window.addEventListener('resize', () => {
		chart.resize(window.innerWidth, window.innerHeight);
	});

    </script>
  </body>
</html>