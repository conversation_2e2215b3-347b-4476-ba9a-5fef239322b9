import os
import sys
import json

import pandas as pd
import numpy as np
from datetime import timedelta

from PyQt6.QtCore import QUrl
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWidgets import QApplication

# -----------------------------------------------------------------------------
# 1. SCALE DOWN A SINGLE BAR
# -----------------------------------------------------------------------------
def scale_down_bar(bar: dict,
                   start_time: pd.Timestamp,
                   end_time: pd.Timestamp,
                   freq: str,
                   high_frac: float = 0.25,
                   low_frac: float = 0.75) -> pd.DataFrame:
    timeline = pd.to_datetime([
        start_time,
        start_time + (end_time - start_time) * high_frac,
        start_time + (end_time - start_time) * low_frac,
        end_time
    ])
    
    prices = [bar["open"], bar["high"], bar["low"], bar["close"]]
    curve = pd.Series(prices, index=timeline).sort_index()

    sub_index = pd.date_range(start_time, end_time, freq=freq)
 
    # interpolate curve at new points
    curve = (
        pd.Series(prices, index=timeline)
          .sort_index()
          .reindex(curve.index.union(sub_index))
          .interpolate(method="time")
          .loc[sub_index]
    )

    # build OHLC
    records = []
    nseg = len(sub_index) - 1

    for i, (t0, t1) in enumerate(zip(sub_index[:-1], sub_index[1:])):
        seg = curve[t0:t1]
        if seg.empty:
            continue

        open_ = seg.iloc[0]
        high  = seg.max()
        low   = seg.min()

        # 最后一个分段强制使用原始 Close
        # if i == nseg - 1:
        #     close = bar["close"]
        # else:
        close = seg.iloc[-1]

        records.append({
            "date":  t0,
            "open":  open_,
            "high":  high,
            "low":   low,
            "close": close,
            "volume":   bar["volume"] / nseg
            
        })

    return pd.DataFrame(records).set_index("date")


def resample_bars(df: pd.DataFrame,
                  from_freq: str,
                  to_freq: str,
                  end_time: pd.Timestamp = None,
                  tradingTimeStart: str = "09:30",
                  tradingTimeEnd: str = "16:00") -> pd.DataFrame:
    df = df.copy()
    df.index = pd.to_datetime(df.index)

    if end_time is not None:
        df = df[df.index <= pd.to_datetime(end_time)]

    from_n = pd.Timedelta(from_freq)
    to_n   = pd.Timedelta(to_freq)

    # UPSAMPLE
    if to_n < from_n:
        pieces = []
        totalRows = len(df) 
        for i, (ts, row) in enumerate(df.iterrows()):
            date = ts.date()
            
            if from_freq.lower().endswith("d"):
                bar_start = pd.Timestamp(f"{date} {tradingTimeStart}")
                bar_end   = pd.Timestamp(f"{date} {tradingTimeEnd}")
            else:
                
                bar_start = ts
                bar_end = ts + from_n
                if i  ==  0 :
                    next_hour = ts.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                    if bar_end >= next_hour:
                        bar_end = bar_end.replace(minute=0, second=0, microsecond=0)
                        if i + 1 < totalRows:
                            if bar_end > df.index[i + 1]:
                                bar_end = df.index[i + 1]
       
            pieces.append(scale_down_bar(
                row.to_dict(), bar_start, bar_end, to_freq
            ))
            
        return pd.concat(pieces).sort_index()

    # DOWNSAMPLE
    ohlc = {
        "open": "first", "high": "max",
        "low":   "min",   "close": "last",
        "volume": "sum",  "barCount": "sum"
    }
    
    agg = df.resample(to_freq).agg(ohlc)
    return agg.drop(columns="WAPxV")


# -----------------------------------------------------------------------------
# 2. BUILD CHART DATA (NAÏVE TIME)
# -----------------------------------------------------------------------------
def build_chart_data_lc(data: list[dict]) -> tuple[list, list]:
    
    priceData  = []
    volumeData = []

    for rec in data:
        ts = rec["date"]                   # pd.Timestamp, naïve
        unix = int(ts.timestamp())         # seconds since epoch

        priceData.append({
            "time":  unix,
            "open":  rec["open"],
            "high":  rec["high"],
            "low":   rec["low"],
            "close": rec["close"]
        })
        volumeData.append({
            "time":  unix,
            "value": rec["volume"],
            "color": "#ef5350" if rec["open"] > rec["close"] else "#26a69a"
        })

    return priceData, volumeData


# -----------------------------------------------------------------------------
# 3. INJECT INTO WEBENGINEVIEW
# -----------------------------------------------------------------------------
def Handle_OnChartViewReady(chartview: QWebEngineView, data: list[dict]):
    priceData, volumeData = build_chart_data_lc(data)

    with open("chart/chart_template.html", encoding="utf-8") as f:
        template = f.read()

    template = template.replace(
        "<!-- TIME_VISIBLE_PLACEHOLDER -->", "true"
    ).replace(
        "<!-- SECONDS_VISIBLE_PLACEHOLDER -->", "true"
    ).replace(
        "<!-- PRICE_DATA_PLACEHOLDER -->", json.dumps(priceData)
    ).replace(
        "<!-- VOLUME_DATA_PLACEHOLDER -->", json.dumps(volumeData)
    )

    open("chart/temp.html", "w", encoding="utf-8").write(template)

    base_path = os.path.abspath("chart") + os.path.sep
    chartview.setHtml(template, QUrl.fromLocalFile(base_path))


# -----------------------------------------------------------------------------
# 4. EXAMPLE MAIN
# -----------------------------------------------------------------------------
if __name__ == "__main__":

    # --- sample daily data ---
    raw = [{'date': '20250729 09:30:00', 'open': 325.63, 'high': 326.25, 'low': 319.51, 'close': 320.3, 'volume': 10161085, 'wap': 321, 'barCount': 43996}, {'date': '20250729 10:00:00', 'open': 320.3, 'high': 321.39, 'low': 318.25, 'close': 319.33, 'volume': 7153220, 'wap': 319, 'barCount': 30771}, {'date': '20250729 10:30:00', 'open': 319.32, 'high': 322.44, 'low': 318.9, 'close': 322.21, 'volume': 6314281, 'wap': 321, 'barCount': 26383}, {'date': '20250729 11:00:00', 'open': 322.17, 'high': 322.63, 'low': 318.37, 'close': 318.51, 'volume': 5421794, 'wap': 320, 'barCount': 22326}, {'date': '20250729 11:30:00', 'open': 318.54, 'high': 320.62, 'low': 318.35, 'close': 320.05, 'volume': 5739918, 'wap': 319, 'barCount': 24166}, {'date': '20250729 12:00:00', 'open': 320.05, 'high': 322.23, 'low': 319.99, 'close': 320.55, 'volume': 4746011, 'wap': 321, 'barCount': 18796}, {'date': '20250729 12:30:00', 'open': 320.55, 'high': 321.05, 'low': 319.65, 'close': 321.04, 'volume': 2827578, 'wap': 320, 'barCount': 11661}, {'date': '20250729 13:00:00', 'open': 321.03, 'high': 321.75, 'low': 320.3, 'close': 320.44, 'volume': 2791635, 'wap': 320, 'barCount': 11120}, {'date': '20250729 13:30:00', 'open': 320.43, 'high': 321.91, 'low': 320.09, 'close': 321.77, 'volume': 2929388, 'wap': 321, 'barCount': 11426}, {'date': '20250729 14:00:00', 'open': 321.8, 'high': 323.78, 'low': 321.31, 'close': 322.63, 'volume': 4789791, 'wap': 322, 'barCount': 19873}, {'date': '20250729 14:30:00', 'open': 322.63, 'high': 323.89, 'low': 322.61, 'close': 322.76, 'volume': 3149212, 'wap': 323, 'barCount': 13483}, {'date': '20250729 15:00:00', 'open': 322.78, 'high': 324.06, 'low': 322.2, 'close': 322.38, 'volume': 3217567, 'wap': 323, 'barCount': 13900}, {'date': '20250729 15:30:00', 'open': 322.4, 'high': 322.55, 'low': 320.77, 'close': 321.4, 'volume': 4527982, 'wap': 321, 'barCount': 21561}]

    json.dump(raw, open("row.json", "w"))

    df_day = (
        pd.DataFrame(raw)
          .rename(columns={"date": "date"})
          .assign(date=lambda d: pd.to_datetime(d["date"]))
          .set_index("date")
    )

    # resample daily → hourly
    pd.set_option("display.max_rows",    None)

    _end = pd.Timestamp("20250729 10:30:00")
    df_hour = resample_bars(df_day, from_freq="30min", to_freq="5min", end_time="20250729 11:00:00")

    df_hour.reset_index().to_json("subrow.json", orient="records")

    # build list of dicts (preserves pd.Timestamp in "date")
    data_list = (
        df_hour
        .reset_index()
        .rename(columns={
            "date":   "date",
            "open":   "open",
            "high":   "high",
            "low":    "low",
            "close":  "close",
            "volume": "volume"
        })
        .to_dict(orient="records")
    )

    # start Qt app
    app = QApplication(sys.argv)
    view = QWebEngineView()
    Handle_OnChartViewReady(view, data_list)
    view.show()
    sys.exit(app.exec())
